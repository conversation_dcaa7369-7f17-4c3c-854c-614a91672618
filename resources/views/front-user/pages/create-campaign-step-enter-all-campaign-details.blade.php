<div class="steps-section create-campaign-top-nav">
    <div class="steps-point">
        <div class="steps-icon">
            <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-1.png') }}" class="step-icon" alt="">
            <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
        </div>
        <div class="steps-cont"></div>
        <div class="steps-which">Social Media</div>
        <div class="steps-position"></div>
    </div>
    <div class="steps-point">
        <div class="steps-icon">
            <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-2.png') }}" class="step-icon" alt="">
            <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
        </div>
        <div class="steps-cont"></div>
        <div class="steps-which">Influencer Tasks</div>
        <div class="steps-position"></div>
    </div>
    <div class="steps-point">
        <div class="steps-icon">
            <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-4-new.svg') }}" class="step-icon" alt="">
            <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
        </div>
        <div class="steps-cont"></div>
        <div class="steps-which">Campaign details</div>
        <div class="steps-position"></div>
    </div>
    <div class="steps-point">
        <div class="steps-icon">
            <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-3-new.svg') }}" class="step-icon" alt="">
            <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
        </div>
        <div class="steps-cont"></div>
        <div class="steps-which">Marketplace</div>
        <div class="steps-position"></div>
    </div>
    <div class="steps-point">
        <div class="steps-icon">
            <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-5.png') }}" class="step-icon" alt="">
            <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
        </div>
        <div class="steps-cont"></div>
        <div class="steps-which">Payment details</div>
        <div class="steps-position"></div>
    </div>
</div>

{{-- MP-Steps --}}
<input type="hidden" name="inputName" id="soc-type" value="" placeholder="">
<div class="this-steps">
    {{-- Step one --}}
    @include('front-user.pages.create-campaign-substep-select-social-media-and-post-type')

    {{-- Step two --}}
    @include('front-user.pages.create-campaign-substep-select-extra-tasks')

    {{-- Step three --}}
    @include('front-user.pages.create-campaign-substep-campaign-details')

    {{-- Step four --}}
    @include('front-user.pages.create-campaign-substep-influencer-marketplace')

    {{-- Step five --}}
    @include('front-user.pages.create-campaign-substep-payment')
</div>

<!--social connect error popup Start end-->
<div class="loaderss" id="pageLoader">
    <img src="{{ asset('/assets/front-end/images/loading-loading-forever.gif') }}" alt="">
</div>


<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.css" rel="stylesheet" type="text/css" />

<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
    // Constants for calculations
    const VAT_RATE = 0.19;
    const ADMIN_COMMISSION_RATE = 5;
    const MIN_PLATFORM_FEE = 2;

    // Initialize step indicators (only if not already initialized)
    var mp_step = $('.create-campaign-top-nav .steps-point');

    var iEra, thumbLi = $('.create-campaign-top-nav .steps-point');
    var iEra_in, thumbLi_in = $('.create-campaign-top-nav .steps-point .steps-cont');

    // Only initialize if IDs are not already set
    if (!thumbLi.first().attr("id")) {
        for (iEra = 0; iEra < mp_step.length; iEra++) {
            thumbLi.eq(iEra).attr("id", 'steps-point' + iEra);
        }

        for (iEra_in = 0; iEra_in < mp_step.length; iEra_in++) {
            thumbLi_in.eq(iEra_in).html('STEP 0' + (iEra_in + 1));
        }
    }

    let max_influcencer_counter = 0;

    // Initialize post type options on page load
    $(document).ready(function() {
        // Reset all radio button states on page load
        resetSocialMediaStep();

        // Show appropriate post type options but don't auto-select
        showPostTypeOptions();
    });

    // Handle boost type selection and show appropriate post type options
    $('input[name="ptc"]').click(function() {
        var select_val = $('input[name="ptc"]:checked').val();
        console.log('Boost type selected, showing post types for:', select_val);

        // Show appropriate post type options
        showPostTypeOptions();

        $("#steps-point0").addClass("inprogress");
        $("#new_steps0").show();
    });

    $('input[name="mp_socialmedia"]').click(function() {
        if ($(this).is(':checked')) {
            console.log('Social media selected:', $(this).val());

            $(".social_media_radio").removeClass("active");
            $(this).closest(".social_media_radio").addClass("active");
            $(".media_platform_post_type").hide();

            // Show the post type options for this social media
            var postTypeContainer = $(this).closest(".media_platform").next(".media_platform_post_type");
            postTypeContainer.css("display", "flex");

            // Show appropriate post type options and update validation
            showPostTypeOptions();
        }
    });

    $(document).on("click", ".button-open-filter", function() {
        $(".filter-button-outer").hide()
        $(".filter-box-outer").show()
        $("select.filter-by").select2();
        $("select").select2();
    });

    $(document).on("click", ".button-close-filter", function() {
        $(".filter-button-outer").show()
        $(".filter-box-outer").hide()
    });

    $(document).on("click", ".step-nevigationbutton > div.influncer-cart", function() {
        $(".selected-influncer-box").toggleClass("open")
        $(".filter-overlay").toggleClass("open")
    });

    $(document).on("click", ".step-nevigationbutton > div > img.close_inf_box", function() {
        $(".selected-influncer-box").toggleClass("open")
        $(".filter-overlay").toggleClass("open")
    });

    $(document).on("click", ".influncer-detail button.select-button", function(event) {
        if (max_influcencer_counter >= 50) {
            toastr.info("Maximum number of influencer limit reached. ");
            return;
        }
        
        const $influencerDetail = $(this).closest(".influncer-detail");
        const get_influncer_id = $influencerDetail.attr("data-id");
        const get_influncer_image = $influencerDetail.find(".profile-pic").html();
        const get_influncer_image_new = $influencerDetail.find(".profile-pic img").attr("src");
        const get_influncer_username = $influencerDetail.find(".user_name").text();
        const is_small_business_owner = Boolean(parseInt($influencerDetail.find("#is_small_business_owner").text() || "0"));
        const get_influncer_follower = parseIntSafe($influencerDetail.find(".follower-count-val").data("followers")) || 0;
        const get_influncer_price = parseFloatSafe($influencerDetail.find(".user_price-val").data("price")) || 0;

        // Update subtotal and follower count
        const currentSubtotal = parseFloatSafe($('#subtotal').data('subtotal') || 0);
        const currentFollower = parseIntSafe($('#follower').data('followers') || 0);
        const newSubtotal = currentSubtotal + get_influncer_price;
        const newFollower = currentFollower + get_influncer_follower;

        // Update with formatted text and data attributes
        $('#subtotal').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);
        $('#follower').text(newFollower).data('followers', newFollower);

        console.log('subtotal: ' + newSubtotal);
        console.log('follower: ' + newFollower);
        console.log('get_influencer_price: ' + get_influncer_price);
        console.log('get_influencer_follower: ' + get_influncer_follower);

        // Add to selected influencers
        var influncer_div = "<div class='influncer-detail card-container text-center mx-auto mt-4' data-id='" +
            get_influncer_id + "'>" + $influencerDetail.html() + "</div>";
        $(".selected-influncer-contr").append(influncer_div);
        $("span.influncer-cart-count").text($(".selected-influncer-contr .influncer-detail").length);
        $(".selected-influncer-contr .influncer-detail button").text("Remove").removeClass("select-button").addClass("remove-button");
        $influencerDetail.addClass("selected");

        $(this).text("UNSELECT").removeClass("select-button").addClass("unselect-button");

        $("#delected-influncer").val($(".selected-influncer-contr .influncer-detail").length);

        const toggleImage = document.getElementById(`card_clickitfame_logo_${get_influncer_id}`);
        if (toggleImage) {
            toggleImage.src = "{{ asset('/assets/front-end/images/icons/clickitfame_white.svg') }}";
        }

        // Add to influencer list
        const influncer_id = get_influncer_id.replace(/inflinser_id/, '');
        const VAT_value = get_influncer_price * VAT_RATE;

        let small_business_owner_pricing =
            '<span id="vat_value" data-vat="0">0</span> € <span style="font-size: 8px; font-weight:100;">(Small business owner according to § 19 UStG)</span>';
        let non_small_business_owner_pricing =
            `<span id="vat_value" data-vat="${VAT_value.toFixed(2)}">${formatNumberWithThousandSeparator(VAT_value)}</span> € <span style="font-size: 8px; font-weight:100;">(VAT 19%)</span>`;

        let VAT_text = is_small_business_owner ? small_business_owner_pricing : non_small_business_owner_pricing;

        // Create influencer list row with data attributes
        var influncer_list_row_new = '<div class="campaign-list ' + get_influncer_id + '">\
                <div class="campaign-item row">\
                    <div class="campaign-influencer col-5" style="margin: 0; display:flex; align-items: center;">\
                        <img src="' + get_influncer_image_new + '" alt="Influencer" class="influencer-pic">\
                        <div class="influencer-details">\
                            <span class="influencer-name" target="_blank" style="color: #212529; ">' +
            get_influncer_username + '</span>\
                            <span class="follower-count-val" data-followers="' + get_influncer_follower + '"><span id="follower-count-val-' + get_influncer_id + '">' + get_influncer_follower + '</span> Followers</span>\
                        </div>\
                    </div>\
                    <div class="campaign-pricing col-6" style="margin: 0; padding:0; display:flex;">\
                        <div class="row w-100">\
                            <span class="user_price-val col-4" data-price="' + get_influncer_price + '">\
                                € <span id="user_price-val-' + get_influncer_id + '">' + formatNumberWithThousandSeparator(get_influncer_price) + '</span></span>\
                            <span class="campaign_vat_price col-8" style="margin: 0; padding:0;">' + VAT_text + '</span>\
                        </div>\
                     </div>\
                   <div class="remove-icon col-1" style="margin: 0; padding:0; text-align: center;">\
                          <img src="{{ asset("/assets/front-end/images/new/delete.svg") }}" data-delete="' +
            get_influncer_id + '">\
                   </div>\
                </div>\
            </div>';
        var influncer_list_row = '<tr class="influncer-detail-final ' + get_influncer_id +
            '"><input type="hidden" name="influncer_selected_id[]" value="' + influncer_id + '">\
                            <td>\
                                <div class="finish-image"> ' + get_influncer_image + ' </div>\
                            </td>\
                            <td class="influncer-name">\
                                ' + get_influncer_username + '\
                            </td>\
                            <td class="total-follower">\
                                <span class="follower-count-val" data-followers="' + get_influncer_follower + '">' + get_influncer_follower + '</span> Follower\
                            </td>\
                            <td class="pricing">\
                                <span class="user_price-val" data-price="' + get_influncer_price + '">' + formatNumberWithThousandSeparator(get_influncer_price) + '</span> €\
                            </td>\
                            <td class="remove-list">\
                                <img src="{{ asset("assets/front-end/images/icons/icon-delete.svg") }}" data-delete="' + get_influncer_id + '" alt="">\
                            </td>\
                        </tr>';

        $(".form-shadow-box.middle-box table tbody").append(influncer_list_row);
        $('#influencers-row').append(influncer_list_row_new);
        
        // Update totals
        updateTotals();
        
        max_influcencer_counter++;
    })

    $(document).on("click", ".influncer-detail button.unselect-button", function() {
        var get_influncer_id = $(this).closest(".influncer-detail").attr("data-id").replace(/inflinser_id/, '');
        $('.selected-influncer-contr div[data-id="inflinser_id' + get_influncer_id + '"]').remove();

        $(".form-shadow-box.middle-box table tbody .inflinser_id" + get_influncer_id).remove();

        $('.influncer-list div[data-id="inflinser_id' + get_influncer_id + '"]').removeClass("selected");
        $("span.influncer-cart-count").text($(".selected-influncer-contr .influncer-detail").length);
        $("#delected-influncer").val($(".selected-influncer-contr .influncer-detail").length);

        const toggleImage = document.getElementById(`card_clickitfame_logo_inflinser_id${get_influncer_id}`);
        if (toggleImage) {
            toggleImage.src = "{{ asset('/assets/front-end/images/icons/clickitfame_logo.svg') }}";
        }

        $('.influncer-list div[data-id="inflinser_id' + get_influncer_id + '"] button.unselect-button')
            .text("SELECT")
            .removeClass("unselect-button")
            .addClass("select-button");

        var get_influncer_follower = parseIntSafe($(this).closest(".influncer-detail").find(".follower-count-val").data("followers")) || 0;
        var get_influncer_price = parseFloatSafe($(this).closest(".influncer-detail").find(".user_price-val").data("price")) || 0;
        var is_small_business_owner = Boolean(parseInt($(this).closest(".influncer-detail").find("#is_small_business_owner").text() || "0"));

        // Update subtotal and follower count
        const currentSubtotal = parseFloatSafe($('#subtotal').data('subtotal') || 0);
        const currentFollower = parseIntSafe($('#follower').data('followers') || 0);
        const newSubtotal = currentSubtotal - get_influncer_price;
        const newFollower = currentFollower - get_influncer_follower;

        // Update with formatted text and data attributes
        $('#subtotal').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);
        $('#follower').text(newFollower).data('followers', newFollower);
        
        // Update totals
        updateTotals();
        
        $(".inflinser_id" + get_influncer_id).remove();
    })

    $(document).on("click", ".influncer-detail button.remove-button", function() {
        var get_influncer_id = $(this).closest(".influncer-detail").attr("data-id").replace(/inflinser_id/, '');
        $(this).closest(".influncer-detail").remove();

        $(".form-shadow-box.middle-box table tbody .inflinser_id" + get_influncer_id).remove();

        $('.influncer-list div[data-id="inflinser_id' + get_influncer_id + '"]').removeClass("selected");
        $("span.influncer-cart-count").text($(".selected-influncer-contr .influncer-detail").length);
        $("#delected-influncer").val($(".selected-influncer-contr .influncer-detail").length);

        const toggleImage = document.getElementById(`card_clickitfame_logo_inflinser_id${get_influncer_id}`);
        if (toggleImage) {
            toggleImage.src = "{{ asset('/assets/front-end/images/icons/clickitfame_logo.svg') }}";
        }

        $('.influncer-list div[data-id="inflinser_id' + get_influncer_id + '"] button.unselect-button')
            .text("SELECT")
            .removeClass("unselect-button")
            .addClass("select-button");

        var get_influncer_follower = parseIntSafe($(this).closest(".influncer-detail").find(".follower-count-val").data("followers")) || 0;
        var get_influncer_price = parseFloatSafe($(this).closest(".influncer-detail").find(".user_price-val").data("price")) || 0;
        var is_small_business_owner = Boolean(parseInt($(this).closest(".influncer-detail").find("#is_small_business_owner").text() || "0"));

        // Update subtotal and follower count
        const currentSubtotal = parseFloatSafe($('#subtotal').data('subtotal') || 0);
        const currentFollower = parseIntSafe($('#follower').data('followers') || 0);
        const newSubtotal = currentSubtotal - get_influncer_price;
        const newFollower = currentFollower - get_influncer_follower;

        // Update with formatted text and data attributes
        $('#subtotal').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);
        $('#follower').text(newFollower).data('followers', newFollower);
        
        // Update totals
        updateTotals();
        
        $(".inflinser_id" + get_influncer_id).remove();
        max_influcencer_counter -= 1;
    });

    // Delete icon click handler in campaign list
    $(document).on("click", ".campaign-item .remove-icon img", function() {
        var get_influncer_id = $(this).data("delete");
        
        // Remove the influencer from various containers
        $('.selected-influncer-contr div[data-id="inflinser_id' + get_influncer_id + '"]').remove();
        $(".form-shadow-box.middle-box table tbody .inflinser_id" + get_influncer_id).remove();
        $('.influncer-list div[data-id="inflinser_id' + get_influncer_id + '"]').removeClass("selected");
        
        // Reset the button state
        $('.influncer-list div[data-id="inflinser_id' + get_influncer_id + '"] button.unselect-button')
            .text("SELECT")
            .removeClass("unselect-button")
            .addClass("select-button");
        
        // Update the logo image
        const toggleImage = document.getElementById(`card_clickitfame_logo_inflinser_id${get_influncer_id}`);
        if (toggleImage) {
            toggleImage.src = "{{ asset('/assets/front-end/images/icons/clickitfame_logo.svg') }}";
        }

        // Get the follower count from the data attribute instead of text
        var get_influncer_follower = parseIntSafe($(this).closest(".campaign-item").find(".follower-count-val").data("followers")) || 0;

        // Get the price from the data attribute
        var get_influncer_price = parseFloatSafe($(this).closest(".campaign-item").find(".user_price-val").data("price")) || 0;

        // Get VAT value if available
        var vat_value = parseFloatSafe($(this).closest(".campaign-item").find("#vat_value").data("vat")) || 0;

        // Update subtotal and follower count
        const currentSubtotal = parseFloatSafe($('#subtotal').data('subtotal') || 0);
        const currentFollower = parseIntSafe($('#follower').data('followers') || 0);
        const newSubtotal = currentSubtotal - get_influncer_price;
        const newFollower = currentFollower - get_influncer_follower;

        // Update all relevant elements with formatted text and data attributes
        $('#subtotal').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);
        $('#subtotal_final').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);
        $('#subtotal_final_new').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);

        $('#follower').text(newFollower).data('followers', newFollower);
        $('#follower_final').text(newFollower).data('followers', newFollower);
        $('#follower_final_new').text(newFollower).data('followers', newFollower);

        // Update totals
        updateTotals();

        // Update counter and remove the element
        max_influcencer_counter -= 1;
        $(this).closest("div.campaign-list").remove();
        $(".influncer-cart span.influncer-cart-count").text(max_influcencer_counter);
    });

    // Steps navigation - next button (only for second-level steps in shoutout section)
    $(document).on("click", ".shoutout .nav-right", function() {
        var currentStep = $(this).closest(".new_steps");
        var current_step_number = parseInt(currentStep.attr("id").replace(/new_steps/, ""));
        var nextStep = current_step_number + 1;

        console.log('current step_number: ' + current_step_number + ', next step: ' + nextStep + ', global step_number: ' + step_number);

        // Validate current step inputs
        var isValid = true;
        currentStep.find(':input:not(:button)').each(function(index, value) {
            if ($(this).parsley) {
                if (!$(this).parsley().validate()) {
                    isValid = false;
                }
            }
            if ($(this).hasClass('tagify--outside')) {
                $(this).siblings('tags').find('span').attr('style', 'border:1px solid red');
            }
        });

        // Additional validation for specific steps
        if (current_step_number == 0) {
            // Validate social media selection
            if (!$('input[name="mp_socialmedia"]:checked').length) {
                isValid = false;
                $("#error-select-social-media").text("Please select social media.");
            } else {
                $("#error-select-social-media").text("");
            }

            // Validate post type selection from visible options only
            var visiblePostTypes = $('.media_platform_post_type .get_type:visible input[name="mp_socialmedia_type2"]');
            var hasVisibleSelection = false;

            visiblePostTypes.each(function() {
                if ($(this).is(':checked')) {
                    hasVisibleSelection = true;
                    return false; // break the loop
                }
            });

            if (visiblePostTypes.length > 0 && !hasVisibleSelection) {
                isValid = false;
                $("#error-post-type").text("Please select post type.");
            } else {
                $("#error-post-type").text("");
            }
        }

        if (current_step_number == 3) {
            // Validate influencer selection
            if ($(".selected-influncer-contr .influncer-detail").length == 0) {
                isValid = false;
                $("#error-select-influncer").removeClass("d-none").text("Please select at least one influencer.");
                return false;
            } else {
                $("#error-select-influncer").addClass("d-none").text("");
            }
        }

        if (isValid && nextStep < 5) {
            // Update global step tracking
            step_number = nextStep;

            // Hide current step and show next step
            currentStep.hide();
            $('#new_steps' + nextStep).show();
            $('#steps-point' + nextStep).addClass("inprogress");
            $('#steps-point' + current_step_number).addClass("completed").removeClass("inprogress");

            // Load tasks for step 1 (step_number 0 -> next step 1)
            if (nextStep == 1) {
                var media = $('input[name="mp_socialmedia"]:checked').val();
                var type = $('input[name="type_post_content"]:checked').val();
                var select_type = $('input[name="mp_socialmedia_type2"]:checked').val();
                var ptc = $('input[name="ptc"]:checked').val();
                $.ajax({
                        url: "{{ url('get-tasks') }}",
                        data: {
                            media: media,
                            type: type,
                            select_type: select_type,
                            ptc: ptc
                        }
                    })
                    .done(function(data) {
                        $("#latest-task").html(data);
                    })
                    .fail(function(xhr, status, error) {
                        console.error("Failed to load tasks:", error);
                    });
            }

            // Load task inputs for step 2 (step_number 1 -> next step 2)
            if (nextStep == 2) {
                var media = $('input[name="mp_socialmedia"]:checked').val();
                var type = $('input[name="type_post_content"]:checked').val();
                var select_type = $('input[name="mp_socialmedia_type2"]:checked').val();
                var ptc = $('input[name="ptc"]:checked').val();

                var task_additional = new Array();
                $(".task_additional:checked").each(function() {
                    task_additional.push($(this).val());
                });

                $.ajax({
                    url: "{{ url('get-tasks-input') }}",
                    data: {
                        media: media,
                        type: type,
                        select_type: select_type,
                        ptc: ptc,
                        task_additional: task_additional
                    }
                })
                .done(function(data) {
                    $("#latest-task-inputs").html(data);

                    // Initialize Tagify if hashtag input exists
                    setTimeout(function() {
                        var input_tag_one = document.querySelector('#hashtag1');
                        if (input_tag_one) {
                            var arrayFromPHP = $("#hashtag_arr").val();
                            if (arrayFromPHP) {
                                new Tagify(input_tag_one, {
                                    whitelist: arrayFromPHP.split(","),
                                });
                            }
                        }
                    }, 100);
                })
                .fail(function(xhr, status, error) {
                    console.error("Failed to load task inputs:", error);
                });
            }

            // Load influencers for step 3 (step_number 2 -> next step 3)
            if (nextStep == 3) {
                var formData = $('#form1').serializeArray();
                var jsonObject = {};
                $.each(formData, function(i, field) {
                    jsonObject[field.name] = field.value;
                });

                jsonObject.media = jsonObject.mp_socialmedia;
                jsonObject.type = jsonObject.type_post_content;
                jsonObject.select_type = jsonObject.mp_socialmedia_type2;

                console.log(jsonObject);

                $.ajax({
                        url: "{{ url('get-influencers') }}",
                        data: jsonObject
                    })
                    .done(function(data) {
                        $("#latest-influencer-lists").html(data);
                        $('#compaingTitle').html($('#campaign_title').val());

                        // Restore selected influencers
                        $(".selected-influncer-contr .influncer-detail").each(function(index) {
                            var selected_attribute = $(this).attr("data-id");
                            $(".influncer-list .influncer-detail[data-id=" + selected_attribute + "]").addClass("selected");

                            $(".influncer-list .influncer-detail[data-id=" + selected_attribute + "] button.select-button")
                                .text("UNSELECT")
                                .removeClass("select-button")
                                .addClass("unselect-button");

                            const toggleImage = document.getElementById(`card_clickitfame_logo_${selected_attribute}`);
                            if (toggleImage) {
                                toggleImage.src = "{{ asset('/assets/front-end/images/icons/clickitfame_white.svg') }}";
                            }
                        });

                        // Initialize load more functionality based on screen size
                        setTimeout(function() {
                            if (typeof $.fn.simpleLoadMore !== 'undefined') {
                                if ($(window).width() > 1399) {
                                    $('.influncer-list').simpleLoadMore({
                                        item: '.influncer-detail',
                                        count: 15,
                                        counterInBtn: true,
                                        btnText: 'View More {showing}/{total}',
                                    });
                                } else if ($(window).width() > 1024) {
                                    $('.influncer-list').simpleLoadMore({
                                        item: '.influncer-detail',
                                        count: 12,
                                        counterInBtn: true,
                                        btnText: 'View More {showing}/{total}',
                                    });
                                } else if ($(window).width() > 991) {
                                    $('.influncer-list').simpleLoadMore({
                                        item: '.influncer-detail',
                                        count: 9,
                                        counterInBtn: true,
                                        btnText: 'View More {showing}/{total}',
                                    });
                                } else if ($(window).width() > 767) {
                                    $('.influncer-list').simpleLoadMore({
                                        item: '.influncer-detail',
                                        count: 6,
                                        counterInBtn: true,
                                        btnText: 'View More {showing}/{total}',
                                    });
                                } else {
                                    $('.influncer-list').simpleLoadMore({
                                        item: '.influncer-detail',
                                        count: 12,
                                        counterInBtn: true,
                                        btnText: 'View More {showing}/{total}',
                                    });
                                }
                            }
                        }, 500);
                    })
                    .fail(function(xhr, status, error) {
                        console.error("Failed to load influencers:", error);
                    });
            }

        }

        if ($('#new_steps3').is(':visible')) {
            $('html, body').animate({
                scrollTop: $('html, body').offset().top,
            });
            var btn = $('body');
            btn.addClass('show');
        }
    });

    // Steps navigation - back button (only for second-level steps in shoutout section)
    $(document).on("click", ".shoutout .nav-left", function() {
        $("#ifdonthave").hide();

        var currentStep = $(this).closest(".new_steps");
        if (currentStep.length && currentStep.attr("id") && currentStep.attr("id").includes("new_steps")) {
            var current_step_number = parseInt(currentStep.attr("id").replace(/new_steps/, ""));
            var prevStep = current_step_number - 1;

            console.log('current step_number: ' + current_step_number + ', prev step: ' + prevStep + ', global step_number: ' + step_number + ', preliminary_step_number: ' + preliminary_step_number);

            if (current_step_number == 0) {
                // If we're at the first second-level step, go back to first-level steps
                var fromStep = $(this).attr("data-from-step");
                if (fromStep == "Reaction video" || fromStep == "Survey") {
                    // Go back to campaign type selection
                    preliminary_step_number = 0;
                    $(".shoutout").addClass("d-none").removeClass("d-block");
                    $(".campaign-type").addClass("d-block").removeClass("d-none");
                } else {
                    // Go back to boost type selection
                    preliminary_step_number = 1;
                    $(".shoutout").addClass("d-none").removeClass("d-block");
                    $(".boost-me").addClass("d-block").removeClass("d-none");
                }

                // Reset all radio button states when going back to first-level steps
                resetSocialMediaStep();

                // Reset step indicators and global step tracking
                step_number = 0;
                $('#steps-point0').removeClass("inprogress completed");
            } else if (prevStep >= 0) {
                // Navigate within second-level steps - preserve radio button states
                step_number = prevStep; // Update global step tracking

                console.log('Navigating back within second-level steps, preserving radio button states');

                currentStep.hide();
                $('#new_steps' + prevStep).show();
                $('#steps-point' + current_step_number).removeClass("inprogress");
                $('#steps-point' + prevStep).addClass("inprogress").removeClass("completed");

                // If returning to social media step (step 0), restore post type visibility
                if (prevStep == 0) {
                    setTimeout(function() {
                        restorePostTypeVisibility();
                    }, 100);
                }
            }

            $(".alert-select-option").addClass("d-none");
        }
    });

    $(document).ready(function() {
        $(document).on('change', '.checkFilter', function() {
            getVenueAjax();
        });
    });

    $(document).on('click', '.resetFilter', function(e) {
        $('#sort_by option[value=""]').attr('selected', 'selected');
        $('#target_age option[value=""]').attr('selected', 'selected');
        $('#language option[value=""]').attr('selected', 'selected');
        $('#content_attracts option[value=""]').attr('selected', 'selected');
        $('#hashtags').val(null).trigger('change');
        $('#influencer_type option[value=""]').attr('selected', 'selected');
        $('#gender option[value=""]').attr('selected', 'selected');
        $('#target_gender option[value=""]').attr('selected', 'selected');
        $('#rank option[value=""]').attr('selected', 'selected');

        $('#followers option[value=""]').attr('selected', 'selected');
        $('#amount-followers').val('0');
        $('#amount-price').val('0');
        getVenueAjax();
    });

    $('.nuwidth-dynamic.proba.dvambers').keyup(function() {
        this.value = this.value.replace(/[^0-9\.]/g, '');
    });

    $(document).on("change", "#selected-files", function() {
        var numFiles = $("input", this)[0].files.length;
    });

    $(document).on("click", ".button-Request", function() {
        if ($(".marketplace-finish .form-shadow-box table tbody").children().length === 0) {
            $("#ifdonthave").show();
            toastr.error("Please select at least one influencer before proceeding.");
            return false;
        } else {
            $("#ifdonthave").hide();
            $("#pageLoader").show();
            // Ensure form is valid before submission
            const $form = $(this).closest('form');
            if ($form.length && typeof $form.parsley === 'function') {
                return $form.parsley().validate();
            }
        }
    });

    // Helper functions for calculations
    function calculatePlatformFee(subtotal) {
        let fee = (subtotal * ADMIN_COMMISSION_RATE) / 100;
        return fee < MIN_PLATFORM_FEE ? MIN_PLATFORM_FEE : fee;
    }

    function parseFloatSafe(value) {
        const parsed = parseFloat(value);
        return isNaN(parsed) ? 0 : parsed;
    }

    function parseIntSafe(value) {
        const parsed = parseInt(value);
        return isNaN(parsed) ? 0 : parsed;
    }

    function formatNumberWithThousandSeparator(value) {
        return parseFloatSafe(value).toLocaleString('de-DE', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    function updateTotals() {
        // Get values from data attributes instead of text
        const subtotal = parseFloatSafe($('#subtotal').data('subtotal'));
        const follower = parseIntSafe($('#follower').data('followers'));
        
        // Update all subtotal and follower displays with formatted text and data attributes
        $('#subtotal_final').text(formatNumberWithThousandSeparator(subtotal)).data('subtotal', subtotal);
        $('#subtotal_final_new').text(formatNumberWithThousandSeparator(subtotal)).data('subtotal', subtotal);
        $('#follower_final').text(follower).data('followers', follower);
        $('#follower_final_new').text(follower).data('followers', follower);
        
        // Calculate platform fee
        const platformFee = calculatePlatformFee(subtotal);
        const platformFeeVAT = platformFee * VAT_RATE;
        
        // Calculate total VAT from selected influencers
        let influencerVAT = 0;
        $(".selected-influncer-contr .influncer-detail").each(function() {
            const price = parseFloatSafe($(this).find(".user_price-val").data("price"));
            const isSmallBusiness = Boolean(parseInt($(this).find("#is_small_business_owner").text() || "0"));
            if (!isSmallBusiness) {
                influencerVAT += price * VAT_RATE;
            }
        });
        
        const totalVAT = influencerVAT + platformFeeVAT;
        const totalPrice = subtotal + platformFee + totalVAT;
        
        // Update all price displays with formatted text and data attributes
        $('#vat_final_new').text(formatNumberWithThousandSeparator(totalVAT)).data('vat', totalVAT);
        $('#total_final').text(formatNumberWithThousandSeparator(totalPrice)).data('total', totalPrice);
        $('#total_final_new').text(formatNumberWithThousandSeparator(totalPrice)).data('total', totalPrice);
        $('#fee_final').text(formatNumberWithThousandSeparator(platformFee)).data('fee', platformFee);
        $('#fee_final_new').text(formatNumberWithThousandSeparator(platformFee)).data('fee', platformFee);
        $('#total_input').val(totalPrice.toFixed(2));
        $('#fee_input').val(platformFee.toFixed(2));
    }

    // Function to reset social media step radio button states
    function resetSocialMediaStep() {
        console.log('Resetting social media step radio button states');

        // Uncheck all social media radios
        $('input[name="mp_socialmedia"]').prop('checked', false);

        // Uncheck all post type radios
        $('input[name="mp_socialmedia_type2"]').prop('checked', false);

        // Remove active class from social media options
        $(".social_media_radio").removeClass("active");

        // Hide all post type containers
        $(".media_platform_post_type").hide();

        // Hide all post type option groups
        $(".media_platform_post_type .get_type").hide();

        // Clear error messages
        $("#error-select-social-media").text("");
        $("#error-post-type").text("");

        // Remove validation attributes
        $('input[name="mp_socialmedia_type2"]').removeAttr('required')
            .removeAttr('data-parsley-errors-container')
            .removeAttr('data-parsley-error-message');
    }

    // Function to update post type validation based on visible options
    function updatePostTypeValidation() {
        // Remove validation from all post type radios first
        $('input[name="mp_socialmedia_type2"]').removeAttr('required')
            .removeAttr('data-parsley-errors-container')
            .removeAttr('data-parsley-error-message');

        // Add validation to the first visible post type radio
        var firstVisiblePostType = $('.media_platform_post_type .get_type:visible input[name="mp_socialmedia_type2"]').first();
        if (firstVisiblePostType.length) {
            firstVisiblePostType.attr('required', 'required')
                .attr('data-parsley-errors-container', '#error-post-type')
                .attr('data-parsley-error-message', 'Please select post type.');
        }

        console.log('Updated validation for visible post types:', $('.media_platform_post_type .get_type:visible').length);
    }

    // Function to show appropriate post type options based on campaign/boost type
    function showPostTypeOptions() {
        var selectedBoostType = $('input[name="ptc"]:checked').val();
        var selectedCampaignType = $('input[name="type_post_content"]:checked').val();

        console.log('Showing post type options for boost type:', selectedBoostType, 'campaign type:', selectedCampaignType);

        // Hide all post type options first
        $(".media_platform_post_type .get_type").hide();

        // Clear any previous selections
        $('input[name="mp_socialmedia_type2"]').prop('checked', false);

        if (selectedBoostType) {
            $(".media_platform_post_type .get_type." + selectedBoostType).show();
        } else if (selectedCampaignType) {
            var campaignClass = selectedCampaignType.toLowerCase().replace(' ', '-');
            $(".media_platform_post_type .get_type." + campaignClass).show();
        }

        // Update validation (no auto-selection)
        updatePostTypeValidation();

        console.log('Visible post type options:', $('.media_platform_post_type .get_type:visible').length);
    }

    // Function to show appropriate post type options while preserving existing selections
    function showPostTypeOptionsPreserveSelection() {
        var selectedBoostType = $('input[name="ptc"]:checked').val();
        var selectedCampaignType = $('input[name="type_post_content"]:checked').val();

        console.log('Showing post type options (preserving selection) for boost type:', selectedBoostType, 'campaign type:', selectedCampaignType);

        // Store current selection before hiding options
        var currentSelection = $('input[name="mp_socialmedia_type2"]:checked').val();

        // Hide all post type options first
        $(".media_platform_post_type .get_type").hide();

        // Show appropriate options based on selection
        if (selectedBoostType) {
            $(".media_platform_post_type .get_type." + selectedBoostType).show();
        } else if (selectedCampaignType) {
            var campaignClass = selectedCampaignType.toLowerCase().replace(' ', '-');
            $(".media_platform_post_type .get_type." + campaignClass).show();
        }

        // Restore the previous selection if it's still visible
        if (currentSelection) {
            var restoredRadio = $('.media_platform_post_type .get_type:visible input[name="mp_socialmedia_type2"][value="' + currentSelection + '"]');
            if (restoredRadio.length) {
                restoredRadio.prop('checked', true);
                console.log('Restored post type selection:', currentSelection);
            }
        }

        // Update validation
        updatePostTypeValidation();

        console.log('Visible post type options (preserved):', $('.media_platform_post_type .get_type:visible').length);
    }

    // When returning to the social media step, restore post type visibility if social media is already selected
    function restorePostTypeVisibility() {
        var selectedSocialMedia = $('input[name="mp_socialmedia"]:checked');
        if (selectedSocialMedia.length) {
            console.log('Restoring post type visibility for selected social media:', selectedSocialMedia.val());

            // Show the post type container for the selected social media
            var postTypeContainer = selectedSocialMedia.closest(".media_platform").next(".media_platform_post_type");
            postTypeContainer.css("display", "flex");

            // Preserve existing post type selection
            var selectedPostType = $('input[name="mp_socialmedia_type2"]:checked').val();
            console.log('Preserving post type selection:', selectedPostType);

            // Show appropriate post type options without clearing selections
            showPostTypeOptionsPreserveSelection();
        }
    }

    function getVenueAjax() {
        const dataString = $("#form1").serialize();
        $.ajax({
            url: "{{ url('/market-step-filter') }}",
            data: dataString
        }).done(function(data) {
            $("#latest-influencer-lists").html(data);
            
            $(".selected-influncer-contr .influncer-detail").each(function() {
                const selected_attribute = $(this).attr("data-id");
                $(".influncer-list .influncer-detail[data-id=" + selected_attribute + "]").addClass("selected");
            });
            
            $(".select").select2();
            $("#hashtags").select2({
                placeholder: "Select",
                multiple: true
            });
        }).fail(function(xhr, status, error) {
            console.error("AJAX request failed:", error);
            toastr.error("Failed to load influencers. Please try again.");
        });
    }
</script>

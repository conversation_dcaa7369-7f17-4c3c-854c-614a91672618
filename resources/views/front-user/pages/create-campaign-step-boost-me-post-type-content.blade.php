<div class="steps-section">
    <h2>What should the influencer boost?</h2>
</div>
{{-- MP-Steps --}}
<div class="page_tab new_steps">
    <div class="alert-select-option d-none mb-3">
        Please seclect an option.
    </div>
    <div class="social_media boost d-flex flex-column align-items-center justify-content-between flex-wrap">
        <div class="social_media_radio campaign-type select-type-post">
            <div class="campaign-type-content">
                <input type="radio" value="content" name="ptc" id="type-post-content">
                <label for="type-post-content">
                    <img src="{{ asset('/assets/front-end/images/icons/icon-bost-share.svg') }}" class="" alt="">
                    <div class="campaign-type-text">Share Content</div>
                    <div class="campaign-type-share-information">The influencer will share your instagram content</div>
                </label>
            </div>
        </div>
        <div class="social_media_radio campaign-type select-type-post">
            <div class="campaign-type-content">
                <input type="radio" value="photo" name="ptc" id="type-post-photo">
                <label for="type-post-photo">
                    <img src="{{ asset('/assets/front-end/images/icons/icon-bost-photo.svg') }}" class="" alt="">
                    <div class="campaign-type-text">Photo</div>
                    <div class="campaign-type-share-information">The influencer will post your photo </div>
                </label>
            </div>
        </div>
        <div class="social_media_radio campaign-type select-type-post">
            <div class="campaign-type-content">
                <input type="radio" value="video" name="ptc" id="type-post-video">
                <label for="type-post-video">
                    <img src="{{ asset('/assets/front-end/images/icons/icon-bost-video.svg') }}" class="" alt="">
                    <div class="campaign-type-text">Video</div>
                    <div class="campaign-type-share-information">The influencer will post your video </div>
                </label>
            </div>
        </div>
    </div>
    <div class="step-nevigationbutton">
        <div class="nav-left start-prev-step">
            <img src="{{ asset('/assets/front-end/images/icons/step-left.svg') }}" class="" alt="">
        </div>
    </div>
</div>
<script>
    $(document).on("click", ".campaign-type a", function(){
        var typeOn = $(this).attr("data-boost-value");
        $("#soc-type").val(typeOn);
        $(".media_platform_post_type").find("."+typeOn).show();
    });

    // Handle selection of boost type and automatically proceed to next step
    $(document).on("click", ".select-type-post", function(){
        var selectedValue = $(this).find('input[name="ptc"]').val();
        console.log('Boost type selected:', selectedValue, 'preliminary_step_number:', preliminary_step_number);

        // Mark the option as selected
        $(".select-type-post").removeClass("selected");
        $(this).addClass("selected");

        // Store the selected value
        $("#soc-type").val(selectedValue);

        // Hide any error messages
        $(".alert-select-option").addClass("d-none");

        // Update step tracking
        preliminary_step_number = 2; // Move to second-level steps
        step_number = 0; // Start at first second-level step

        // Automatically proceed to the next step after a short delay for visual feedback
        setTimeout(function() {
            // Hide current step and show next step
            $(".boost-me").addClass("d-none").removeClass("d-block");
            $(".shoutout").removeClass("d-none").addClass("d-block");

            // Reset radio button states when entering from boost type selection
            if (typeof resetSocialMediaStep === 'function') {
                resetSocialMediaStep();
            }

            // Initialize the second-level steps
            $(".media_platform_post_type").find(".get_type").hide();
            $(".media_platform_post_type").find(".get_type." + selectedValue).show();
            $("#steps-point0").addClass("inprogress");
            $("#new_steps0").show();

            // Show appropriate post type options
            if (typeof showPostTypeOptions === 'function') {
                showPostTypeOptions();
            }
        }, 300); // Small delay for better UX
    });

    // Handle back navigation from step-two
    $(document).on("click", ".boost-me .start-prev-step", function() {
        console.log('Navigating back from step-two to step-one, preliminary_step_number:', preliminary_step_number);

        // Update step tracking
        preliminary_step_number = 0; // Back to campaign type selection

        $(".boost-me").addClass("d-none").removeClass("d-block");
        $(".campaign-type").addClass("d-block").removeClass("d-none");

        // Clear any previous selections to allow re-selection
        $('input[name="ptc"]').prop('checked', false);
        $(".select-type-post").removeClass("selected");
        $(".alert-select-option").addClass("d-none");
    });
</script>
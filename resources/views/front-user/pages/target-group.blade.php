<div class="mobile-step-detail">
    <div class="steps-cont">
        STEP 04
    </div>
    <div class="steps-which">
        Select target group
    </div>
</div>
<form action="{{ url('/save-influencer-form') }}" method="post" data-parsley-validate id="draft2">
    @csrf
    <div class="informationDiv targetGroup">
    <h2 class="gnrlInformation">Target group</h2>
    <input type="hidden" name="collection" value="2">
    <div class="connectPrising ">
    <div class="row">
            <div class="col-md-6 reqr">
                <div class="">
                    
                    <label>Average age of the followers <span class="color-red">*</span></label>
                    <div class="floating-label">
                        <select class="form-control select service-select required" id="averageage" name="ages" data-parsley-required-message="Please select age of the followers." data-parsley-errors-container="#error-af" required>
                            <option value=''>Please choose..</option> 
                            <option @if(isset($influencer_detail->ages) && $influencer_detail->ages == 'All ages') selected @endif  >All ages</option>
                            <option @if(isset($influencer_detail->ages) && $influencer_detail->ages == 'Children') selected @endif  >Children</option>
                            <option @if(isset($influencer_detail->ages) && $influencer_detail->ages == 'Middle-Aged') selected @endif  >Middle-Aged</option>
                            <option @if(isset($influencer_detail->ages) && $influencer_detail->ages == 'Elderly') selected @endif  >Elderly</option>
                        </select>
                        {{-- <span class="nextLinkReq onClkOk rqrIn1" id="targetAge1"><span>1</span> Next</span> --}}
                        <div class="required_message">Please select age of the followers.</div>
                        <span id="error-af"></span>
                    </div>
                </div>
            </div>
            <div class="col-md-6 reqr">
                <div class="">
                    <label>Content-Language <span class="color-red">*</span></label>
                    <div class="floating-label">
                        <select class="select form-control service-select required" id="contentLangage" name="content_language" data-parsley-required-message="Please select Content-Language." data-parsley-errors-container="#error-cl" required>
                            <option value=''>Please choose..</option>
                            @if(isset($influencer_detail->content_language) && $influencer_detail->content_language!=''  ) 
                                <option selected >{{$influencer_detail->content_language}}</option>  
                                @endif  

                                <option value="Afrikaans">Afrikaans</option>
                                <option value="Albanian">Albanian</option>
                                <option value="Arabic">Arabic</option>
                                <option value="Armenian">Armenian</option>
                                <option value="Basque">Basque</option>
                                <option value="Bengali">Bengali</option>
                                <option value="Bulgarian">Bulgarian</option>
                                <option value="Catalan">Catalan</option>
                                <option value="Cambodian">Cambodian</option>
                                <option value="Chinese (Mandarin)">Chinese (Mandarin)</option>
                                <option value="Croatian">Croatian</option>
                                <option value="Czech">Czech</option>
                                <option value="Danish">Danish</option>
                                <option value="Dutch">Dutch</option>
                                <option value="English" >English</option>
                                <option value="Estonian">Estonian</option>
                                <option value="Fiji">Fiji</option>
                                <option value="Finnish">Finnish</option>
                                <option value="French">French</option>
                                <option value="Georgian">Georgian</option>
                                <option value="German">German</option>
                                <option value="Greek">Greek</option>
                                <option value="Gujarati">Gujarati</option>
                                <option value="Hebrew">Hebrew</option>
                                <option value="Hindi">Hindi</option>
                                <option value="Hungarian">Hungarian</option>
                                <option value="Icelandic">Icelandic</option>
                                <option value="Indonesian">Indonesian</option>
                                <option value="Irish">Irish</option>
                                <option value="Italian">Italian</option>
                                <option value="Japanese">Japanese</option>
                                <option value="Javanese">Javanese</option>
                                <option value="Korean">Korean</option>
                                <option value="Latin">Latin</option>
                                <option value="Latvian">Latvian</option>
                                <option value="Lithuanian">Lithuanian</option>
                                <option value="Macedonian">Macedonian</option>
                                <option value="Malay">Malay</option>
                                <option value="Malayalam">Malayalam</option>
                                <option value="Maltese">Maltese</option>
                                <option value="Maori">Maori</option>
                                <option value="Marathi">Marathi</option>
                                <option value="Mongolian">Mongolian</option>
                                <option value="Nepali">Nepali</option>
                                <option value="Norwegian">Norwegian</option>
                                <option value="Persian">Persian</option>
                                <option value="Polish">Polish</option>
                                <option value="Portuguese">Portuguese</option>
                                <option value="Punjabi">Punjabi</option>
                                <option value="Quechua">Quechua</option>
                                <option value="Romanian">Romanian</option>
                                <option value="Russian">Russian</option>
                                <option value="Samoan">Samoan</option>
                                <option value="Serbian">Serbian</option>
                                <option value="Slovak">Slovak</option>
                                <option value="Slovenian">Slovenian</option>
                                <option value="Spanish">Spanish</option>
                                <option value="Swahili">Swahili</option>
                                <option value="Swedish ">Swedish </option>
                                <option value="Tamil">Tamil</option>
                                <option value="Tatar">Tatar</option>
                                <option value="Telugu">Telugu</option>
                                <option value="Thai">Thai</option>
                                <option value="Tibetan">Tibetan</option>
                                <option value="Tonga">Tonga</option>
                                <option value="Turkish">Turkish</option>
                                <option value="Ukrainian">Ukrainian</option>
                                <option value="Urdu">Urdu</option>
                                <option value="Uzbek">Uzbek</option>
                                <option value="Vietnamese">Vietnamese</option>
                                <option value="Welsh">Welsh</option>
                                <option value="Xhosa">Xhosa</option>

                        </select>
                        <!-- <span class="nextLinkReq onClkOk rqrIn1" id="targetAge2"><span>2</span> Next</span> -->
                        <div class="required_message">Please select Content-Language.</div>
                        <span id="error-cl"></span>
                    </div>
                </div>
            </div>
            <hr />
            <div class="col-md-6">
                <div class="form-group ">
                    <label>The content attracts mostly <span class="color-red">*</span></label>
                    <div class="cusomRadio">
                        <div class="form-check ps-0">
                            <input class="form-check-input service-radio required" type="radio" name="content_attracts" id="attracts1" value="Male"  data-parsley-required-message="Please select content attracts." @if(isset($influencer_detail->content_attracts) && $influencer_detail->content_attracts == 'Male') checked  @endif data-parsley-errors-container="#error-cam" required>
                            <label class="form-check-label" for="attracts1">
                                Male
                            </label>
                            <div class="required_message">Please select content attracts.</div>
                        </div>
                        <div class="form-check ps-0">
                            <input class="form-check-input service-radio required" type="radio" name="content_attracts" id="attracts2" value="Female"  data-parsley-required-message="Please select content attracts." @if(isset($influencer_detail->content_attracts) && $influencer_detail->content_attracts == 'Female') checked @endif> 
                            <label class="form-check-label" for="attracts2">
                                Female
                            </label>
                        </div>
                        <div class="form-check ps-0">
                            <input class="form-check-input service-radio required" type="radio" name="content_attracts" id="attracts3" value="Mixed"  data-parsley-required-message="Please select content attracts."  @if(isset($influencer_detail->content_attracts) && $influencer_detail->content_attracts == 'Mixed') checked @endif>
                            <label class="form-check-label" for="attracts3">
                                Mixed
                            </label>
                        </div>
                    </div>
                    <span id="error-cam"></span>
                </div>                                        
            </div>

        </div>
    </div>
    <input type="hidden" name="publish" value="Publish"> 
    <div class="step-nevigationbutton">
        <div class="nav-left back me-2" id="Back_step2">
            <img src="{{ asset('assets/front-end/images/icons/step-left.svg') }}" alt="">
        </div>
        <div class="nav-right next ms-auto">
            <img src="{{ asset('assets/front-end/images/icons/step-right.svg') }}" alt="">
        </div>
    </div>
    </div>
</form>

<script>
    $(".onClkOk").click(function(){
        var isValid = true;
        $('#draft2 .form-control[required]').each(function(e) {
            if ($(this).val() === '' ) {
                $(".onClkOk").hide();
                $(this).closest(".floating-label").find(".onClkOk").show();
                isValid = false;
                e.stopPropagation();
            }
        });

        return isValid;
    });

</script>
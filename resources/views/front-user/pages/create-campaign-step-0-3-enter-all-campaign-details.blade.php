<div class="steps-section create-campaign-top-nav">
    <div class="steps-point">
        <div class="steps-icon">
            <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-1.png') }}" class="step-icon" alt="">
            <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
        </div>
        <div class="steps-cont"></div>
        <div class="steps-which">Social Media</div>
        <div class="steps-position"></div>
    </div>
    <div class="steps-point">
        <div class="steps-icon">
            <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-2.png') }}" class="step-icon" alt="">
            <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
        </div>
        <div class="steps-cont"></div>
        <div class="steps-which">Influencer Tasks</div>
        <div class="steps-position"></div>
    </div>
    <div class="steps-point">
        <div class="steps-icon">
            <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-4-new.svg') }}" class="step-icon" alt="">
            <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
        </div>
        <div class="steps-cont"></div>
        <div class="steps-which">Campaign details</div>
        <div class="steps-position"></div>
    </div>
    <div class="steps-point">
        <div class="steps-icon">
            <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-3-new.svg') }}" class="step-icon" alt="">
            <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
        </div>
        <div class="steps-cont"></div>
        <div class="steps-which">Marketplace</div>
        <div class="steps-position"></div>
    </div>
    <div class="steps-point">
        <div class="steps-icon">
            <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-5.png') }}" class="step-icon" alt="">
            <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
        </div>
        <div class="steps-cont"></div>
        <div class="steps-which">Payment details</div>
        <div class="steps-position"></div>
    </div>
</div>

{{-- MP-Steps --}}
<input type="hidden" name="inputName" id="soc-type" value="" placeholder="">
<div class="this-steps">
    {{-- Step one --}}
    @include('front-user.pages.create-campaign-substep-0-3-1-select-social-media-and-post-type')

    {{-- Step two --}}
    @include('front-user.pages.create-campaign-substep-0-3-2-select-extra-tasks')

    {{-- Step three --}}
    @include('front-user.pages.create-campaign-substep-0-3-3-campaign-details')

    {{-- Step four --}}
    @include('front-user.pages.create-campaign-substep-0-3-4-influencer-marketplace')

    {{-- Step five --}}
    @include('front-user.pages.create-campaign-substep-0-3-5-payment')
</div>

<!--social connect error popup Start end-->
<div class="loaderss" id="pageLoader">
    <img src="{{ asset('/assets/front-end/images/loading-loading-forever.gif') }}" alt="">
</div>

<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.css" rel="stylesheet" type="text/css" />

<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

@section('script_links')
    <script>
        url: "{{ url('get-tasks-input') }}",
        window.Laravel = {
            urls: {
                getTasks: "{{ url('get-tasks') }}",
                getTasksInput: "{{ url('get-tasks-input') }}",
                getInfluencers: "{{ url('get-influencers') }}",
                marketStepFilter: "{{ url('/market-step-filter') }}",
            },
            csrfToken: "{{ csrf_token() }}",
            user: @json(auth()->user())
        };
    </script>
    <script src="{{ asset('js/create-campaign.js') }}"></script>
@endsection

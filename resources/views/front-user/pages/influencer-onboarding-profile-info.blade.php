<div class="mobile-step-detail">
    <div class="steps-cont">
        STEP 01
    </div>
    <div class="steps-which">
        Profile Information
    </div>
</div>
<form action="{{ url('/my-profile') }}" method="post" data-parsley-validate id="draft0">
    @csrf
    <div class="row">
        <div class="col-12 text-center">
            @if (!$stripeAccount)
                <a class="profile-connect-btn" href="https://connect.stripe.com/oauth/authorize?response_type=code&client_id={{ config('settings.env.STRIPE_CLIENT_ID') }}&redirect_uri={{ url('connect-stripe-account') }}&scope=read_write&stripe_user[email]={{ Auth::user()->email }}">
                    Connect with Stripe
                </a>
                <input type="hidden" id="stripe_status" value="not_connected">
            @else
                @php
                    $social_connect_media = App\Models\SocialConnect::where('user_id', Auth::id())->first();
                @endphp
                <div class="d-flex flex-column justify-center align-items-center">
                    <input type="hidden" id="stripe_status" value="connected">
                </div>
            @endif
        </div>

        <div class="col-12"></div>

        <div class="col-lg-6 col-12">
            <div class="form-group" @if(!$stripeAccount) disabled @endif>
                <label class="floatLabel">First Name <span class="color-red">*</span></label>
                <div class="floating-label smalSpace">
                    <input type="text" class="form-control floating-input profile-input" id="rqrIn1" keypress="doFocus(1)" placeholder="First Name" name="first_name" required="" data-parsley-required-message="Please enter first name." value="{{ Auth::user()->first_name }}">
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-12">
            <div class="form-group" @if(!$stripeAccount) disabled @endif>
                <label class="floatLabel">Last Name <span class="color-red">*</span></label>
                <div class="floating-label smalSpace">
                    <input type="text" class="form-control floating-input profile-input" id="rqrIn2" keypress="doFocus(2)" placeholder="Last Name" name="last_name" required="" data-parsley-required-message="Please enter last name." value="{{ Auth::user()->last_name }}">
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-12">
            <div class="form-group" @if(!$stripeAccount) disabled @endif>
                <label class="floatLabel">Email Id</label>
                <div class="floating-label smalSpace">
                    <input type="email" class="form-control floating-input profile-input" placeholder="Email Id" name="email" readonly="" value="{{ Auth::user()->email }}">
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-12">
            <div class="form-group" @if(!$stripeAccount) disabled @endif>
                <label class="floatLabel">Phone Number</label>
                <div class="floating-label smalSpace">
                    <input type="number" class="form-control floating-input profile-input optional-input" placeholder="Phone Number" id="oprIn1" name="phone" data-parsley-required-message="Please enter phone." data-parsley-type="digits" data-parsley-type-message="Please enter a valid phone number." data-parsley-maxlength="20" data-parsley-maxlength-message="Max length 20 numbers." value="{{ Auth::user()->phone }}">
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-12">
            <div class="form-group" @if(!$stripeAccount) disabled @endif>
                <label class="floatLabel">
                    Company Name
                    @if (Auth::user()->user_type == 'customer')
                        <span class="color-red">*</span>
                    @endif
                </label>
                <div class="floating-label smalSpace">
                    <input type="text" class="form-control floating-input profile-input optional-input" placeholder="Company Name" id="oprIn2" name="company_name" value="{{ Auth::user()->company_name }}" @if (Auth::user()->user_type == 'customer') required @endif data-parsley-required-message="Please enter Company Name.">
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-12">
            <div class="form-group" @if(!$stripeAccount) disabled @endif>
                <label class="floatLabel">Tax ID <span class="color-red">*</span></label>
                <div class="floating-label smalSpace">
                    <input type="text" class="form-control floating-input profile-input optional-input" placeholder="Tax ID" id="oprIn5" name="vat_id" value="{{ Auth::user()->vat_id }}" required data-parsley-required-message="Please enter VAT or Tax ID." data-parsley-pattern="^(?:[A-Z]{2}\d+|\d+)$" data-parsley-length="[9, 11]" data-parsley-pattern-message="Enter a valid VAT ID (e.g., DE123456789) or Tax ID (***********).">
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6 col-12 reqr">
            <div class="form-group" @if(!$stripeAccount) disabled @endif>
                <label class="floatLabel">Country <span class="color-red">*</span></label>
                <div class="floating-label smalSpace reqr">
                    <select class="select form-control floating-input profile-select" name="country" required="" id="rqrIn3country" data-parsley-required-message="Please enter Country." data-parsley-errors-container="#error-countery">
                        <option value="">Select Country</option>
                        @foreach ($countries as $country)
                            <option @if (Auth::user()->country == $country->id) Selected @endif value="{{ $country->id }}">{{ $country->name }}</option>
                        @endforeach
                    </select>
                    <span id="error-countery"></span>
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-12 reqr">
            <div class="form-group" @if(!$stripeAccount) disabled @endif>
                <label class="floatLabel">City <span class="color-red">*</span></label>
                <div class="floating-label smalSpace reqr">
                    <select class="select form-control floating-input profile-select" name="city" required="" id="rqrIn5" data-parsley-required-message="Please enter City." data-parsley-errors-container="#error-city" @if (Auth::user()->country == '') disabled @endif>
                        <option value="">Select City</option>
                        @foreach ($cities as $row)
                            @if($row->name != '')
                                <option value="{{ $row->id }}" @if (Auth::user()->city == $row->id) Selected @endif>{{ $row->name }}</option>
                            @endif
                        @endforeach
                    </select>
                    <span id="error-city"></span>
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-12">
            <div class="form-group" @if(!$stripeAccount) disabled @endif>
                <label class="floatLabel">Street</label>
                <div class="floating-label smalSpace">
                    <input type="text" class="form-control floating-input profile-input optional-input" placeholder="Street" id="oprIn3" name="street" data-parsley-required-message="Please enter street." value="{{ Auth::user()->street }}">
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-12">
            <div class="form-group" @if(!$stripeAccount) disabled @endif>
                <label class="floatLabel">Post/Zip Code <span class="color-red">*</span></label>
                <div class="floating-label smalSpace">
                    <input type="text" class="form-control floating-input profile-input zip_code optional-input" name="zip_code" id="oprIn4" placeholder="Post/Zip Code" required="" data-parsley-required-message="Please enter post/zip code." value="{{ Auth::user()->zip_code }}">
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="floatLabel">
            Are you a small business owner (according to <a href="https://www.gesetze-im-internet.de/ustg_1980/__19.html" target="_blank">§ 19 UStG</a>)?
            <span class="color-red">*</span>
        </label>
        <div class="floating-label smalSpace">
            <div class="radio-group" style="display: flex; gap: 40px; padding-left: 30px; margin-top: 10px;">
                <div class="form-check">
                    <input type="radio" class="form-check-input" name="is_small_business_owner" id="is_small_business_owner_yes" value="1" {{ Auth::user()->is_small_business_owner == 1 ? 'checked' : '' }} required data-parsley-required-message="Please select whether you are a small business owner." data-parsley-errors-container="#small-business-error"/>
                    <label class="form-check-label" for="is_small_business_owner_yes" style="margin-left: 5px;">
                        Yes
                    </label>
                </div>
                <div class="form-check">
                    <input type="radio" class="form-check-input" name="is_small_business_owner" id="is_small_business_owner_no" value="0" {{ Auth::user()->is_small_business_owner == 0 ? 'checked' : '' }} required data-parsley-required-message="Please select whether you are a small business owner." data-parsley-errors-container="#small-business-error"/>
                    <label class="form-check-label" for="is_small_business_owner_no" style="margin-left: 5px;">
                        No
                    </label>
                </div>
            </div>
            <span id="small-business-error" class="text-danger" style="padding-left: 30px; display: block; margin-top: 5px;"></span>
        </div>
    </div>

    <div class="step-nevigationbutton">
        <div class="nav-right next ms-auto">
            <img src="{{ asset('assets/front-end/images/icons/step-right.svg') }}" alt="Next">
        </div>
    </div>
</form>
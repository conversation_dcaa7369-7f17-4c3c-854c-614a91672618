@extends('front-user.layouts.master_user_dashboard')

@section('content')
<style>
    .onboarding-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .step-indicator {
        display: flex;
        justify-content: space-between;
        margin-bottom: 40px;
        position: relative;
    }

    .step-indicator::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 2px;
        background: #e9ecef;
        z-index: 1;
    }

    .step-tab {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #6c757d;
        background: white;
        padding: 10px;
        border-radius: 8px;
        min-width: 120px;
        position: relative;
        z-index: 2;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .step-tab.active {
        color: #AD80FF;
        background: #f8f9fa;
        border: 2px solid #AD80FF;
    }

    .step-tab.completed {
        color: #28a745;
        background: #d4edda;
    }

    .step-tab.disabled {
        color: #adb5bd;
        cursor: not-allowed;
        opacity: 0.6;
    }

    .step-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        background: #e9ecef;
    }

    .step-tab.active .step-icon {
        background: #AD80FF;
        color: white;
    }

    .step-tab.completed .step-icon {
        background: #28a745;
        color: white;
    }

    .step-content {
        background: white;
        border-radius: 12px;
        padding: 30px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        min-height: 500px;
    }

    .navigation-buttons {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
    }

    .btn-onboarding {
        padding: 12px 30px;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
    }

    .btn-onboarding-primary {
        background: #AD80FF;
        color: white;
        border: none;
    }

    .btn-onboarding-primary:hover {
        background: #9966ff;
        color: white;
    }

    .btn-onboarding-secondary {
        background: #6c757d;
        color: white;
        border: none;
    }

    .btn-onboarding-secondary:hover {
        background: #5a6268;
        color: white;
    }

    @media (max-width: 768px) {
        .step-indicator {
            flex-wrap: wrap;
            gap: 10px;
        }

        .step-tab {
            min-width: 100px;
            font-size: 12px;
        }

        .step-content {
            padding: 20px;
        }
    }
</style>

@php
    // Get current user's onboarding data
    $currentUser = Auth::user();
    $influencerDetail = App\Models\InfluencerDetail::where('user_id', Auth::id())->first();
    $stripeAccount = App\Models\StripeAccount::where('user_id', Auth::id())->first();
    $socialConnections = App\Models\SocialConnect::where('user_id', Auth::id())->get();
    $countries = App\Models\Country::get();
    $categories = App\Models\Category::get();

    // Calculate campaign counts for go-live status
    $activeCampaignCount = App\Models\InfluencerRequestDetail::where('influencer_detail_id', @$influencerDetail->id)
        ->where('finish', NULL)
        ->where('refund_reason', NULL)
        ->where('review', '!=', 1)
        ->count();
@endphp

<section class="onboarding-container">
    <!-- Step Indicator -->
    <div class="step-indicator">
        <a href="#profile-information" class="step-tab" data-step="profile-information">
            <div class="step-icon">
                <img src="{{ asset('/assets/front-end/images/icons/profile_info.svg') }}" alt="Profile" width="20">
            </div>
            <span class="step-text">Profile Information</span>
        </a>

        <a href="#social-media" class="step-tab" data-step="social-media">
            <div class="step-icon">
                <img src="{{ asset('/assets/front-end/images/icons/social_media.svg') }}" alt="Social Media" width="20">
            </div>
            <span class="step-text">Social Media</span>
        </a>

        <a href="#general-information" class="step-tab" data-step="general-information">
            <div class="step-icon">
                <img src="{{ asset('/assets/front-end/images/icons/general_info.svg') }}" alt="General Info" width="20">
            </div>
            <span class="step-text">General Information</span>
        </a>

        <a href="#target-group" class="step-tab" data-step="target-group">
            <div class="step-icon">
                <img src="{{ asset('/assets/front-end/images/icons/oi_target.svg') }}" alt="Target Group" width="20">
            </div>
            <span class="step-text">Target Group</span>
        </a>

        <a href="#campaign-types" class="step-tab" data-step="campaign-types">
            <div class="step-icon">
                <img src="{{ asset('/assets/front-end/images/icons/campaign_type.svg') }}" alt="Campaign Types" width="20">
            </div>
            <span class="step-text">Campaign Types</span>
        </a>

        <a href="#go-live" class="step-tab" data-step="go-live">
            <div class="step-icon">
                @if($currentUser->status == 1 && $activeCampaignCount < 5)
                    <img src="{{ asset('/assets/front-end/images/icons/go_live.svg') }}" alt="Go Live" width="20">
                @else
                    <img src="{{ asset('/assets/front-end/images/icons/market_place_offline.svg') }}" alt="Offline" width="20">
                @endif
            </div>
            <span class="step-text">Go Live</span>
        </a>
    </div>

    <!-- Step Content Container -->
    <div class="step-content">
        <!-- Hidden inputs for state management -->
        <input type="hidden" id="stripe_status" value="{{ $stripeAccount ? 'connected' : 'not_connected' }}">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <!-- Step 1: Profile Information -->
        <div class="onboarding-step-content" data-step="profile-information">
            <h2 class="mb-4">Profile Information</h2>

            @if (!$stripeAccount)
                <div class="alert alert-warning text-center mb-4">
                    <h5>Connect with Stripe to Continue</h5>
                    <p>You need to connect your Stripe account before proceeding with the onboarding process.</p>
                    <a class="btn btn-primary" href="https://connect.stripe.com/oauth/authorize?response_type=code&client_id={{ config('settings.env.STRIPE_CLIENT_ID') }}&redirect_uri={{ url('connect-stripe-account') }}&scope=read_write&stripe_user[email]={{ $currentUser->email }}">
                        Connect with Stripe
                    </a>
                </div>
            @endif

            <form action="{{ url('/my-profile') }}" method="post" class="onboarding-form" data-parsley-validate>
                @csrf
                <div class="row">
                    <div class="col-lg-6 col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">First Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="first_name" required
                                   placeholder="Enter your first name"
                                   value="{{ $currentUser->first_name }}"
                                   {{ !$stripeAccount ? 'disabled' : '' }}>
                            <div class="error-message text-danger mt-1" style="display: none;"></div>
                        </div>
                    </div>

                    <div class="col-lg-6 col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">Last Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="last_name" required
                                   placeholder="Enter your last name"
                                   value="{{ $currentUser->last_name }}"
                                   {{ !$stripeAccount ? 'disabled' : '' }}>
                            <div class="error-message text-danger mt-1" style="display: none;"></div>
                        </div>
                    </div>

                    <div class="col-lg-6 col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">Email Address</label>
                            <input type="email" class="form-control" name="email" readonly
                                   value="{{ $currentUser->email }}">
                        </div>
                    </div>

                    <div class="col-lg-6 col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" name="phone"
                                   placeholder="Enter your phone number"
                                   value="{{ $currentUser->phone }}"
                                   {{ !$stripeAccount ? 'disabled' : '' }}>
                            <div class="error-message text-danger mt-1" style="display: none;"></div>
                        </div>
                    </div>

                    <div class="col-lg-6 col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">
                                Company Name
                                @if ($currentUser->user_type == 'customer')
                                    <span class="text-danger">*</span>
                                @endif
                            </label>
                            <input type="text" class="form-control" name="company_name"
                                   placeholder="Enter your company name"
                                   value="{{ $currentUser->company_name }}"
                                   {{ $currentUser->user_type == 'customer' ? 'required' : '' }}
                                   {{ !$stripeAccount ? 'disabled' : '' }}>
                            <div class="error-message text-danger mt-1" style="display: none;"></div>
                        </div>
                    </div>

                    <div class="col-lg-6 col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">Tax ID <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="vat_id" required
                                   placeholder="Enter your VAT or Tax ID"
                                   value="{{ $currentUser->vat_id }}"
                                   pattern="^(?:[A-Z]{2}\d+|\d+)$"
                                   {{ !$stripeAccount ? 'disabled' : '' }}>
                            <small class="form-text text-muted">Enter a valid VAT ID (e.g., DE123456789) or Tax ID</small>
                            <div class="error-message text-danger mt-1" style="display: none;"></div>
                        </div>
                    </div>

                    <div class="col-lg-6 col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">Country <span class="text-danger">*</span></label>
                            <select class="form-select" name="country" required {{ !$stripeAccount ? 'disabled' : '' }}>
                                <option value="">Select Country</option>
                                @foreach ($countries as $country)
                                    <option value="{{ $country->id }}" {{ $currentUser->country == $country->id ? 'selected' : '' }}>
                                        {{ $country->name }}
                                    </option>
                                @endforeach
                            </select>
                            <div class="error-message text-danger mt-1" style="display: none;"></div>
                        </div>
                    </div>

                    <div class="col-lg-6 col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">City <span class="text-danger">*</span></label>
                            <select class="form-select" name="city" required
                                    {{ !$stripeAccount || !$currentUser->country ? 'disabled' : '' }}>
                                <option value="">Select City</option>
                                @if($currentUser->country)
                                    @php
                                        $cities = DB::table('states')
                                            ->leftjoin('cities', 'states.id', '=', 'cities.state_id')
                                            ->select('cities.*')
                                            ->where('states.country_id', $currentUser->country)
                                            ->get();
                                    @endphp
                                    @foreach ($cities as $city)
                                        @if($city->name)
                                            <option value="{{ $city->id }}" {{ $currentUser->city == $city->id ? 'selected' : '' }}>
                                                {{ $city->name }}
                                            </option>
                                        @endif
                                    @endforeach
                                @endif
                            </select>
                            <div class="error-message text-danger mt-1" style="display: none;"></div>
                        </div>
                    </div>

                    <div class="col-lg-6 col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">Street Address</label>
                            <input type="text" class="form-control" name="street"
                                   placeholder="Enter your street address"
                                   value="{{ $currentUser->street }}"
                                   {{ !$stripeAccount ? 'disabled' : '' }}>
                            <div class="error-message text-danger mt-1" style="display: none;"></div>
                        </div>
                    </div>

                    <div class="col-lg-6 col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">Post/Zip Code <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="zip_code" required
                                   placeholder="Enter your postal code"
                                   value="{{ $currentUser->zip_code }}"
                                   {{ !$stripeAccount ? 'disabled' : '' }}>
                            <div class="error-message text-danger mt-1" style="display: none;"></div>
                        </div>
                    </div>

                    <div class="col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">
                                Are you a small business owner (according to <a href="https://www.gesetze-im-internet.de/ustg_1980/__19.html" target="_blank">§ 19 UStG</a>)?
                                <span class="text-danger">*</span>
                            </label>
                            <div class="mt-2">
                                <div class="form-check form-check-inline">
                                    <input type="radio" class="form-check-input" name="is_small_business_owner"
                                           id="small_business_yes" value="1" required
                                           {{ $currentUser->is_small_business_owner == 1 ? 'checked' : '' }}
                                           {{ !$stripeAccount ? 'disabled' : '' }}>
                                    <label class="form-check-label" for="small_business_yes">Yes</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input type="radio" class="form-check-input" name="is_small_business_owner"
                                           id="small_business_no" value="0" required
                                           {{ $currentUser->is_small_business_owner == 0 ? 'checked' : '' }}
                                           {{ !$stripeAccount ? 'disabled' : '' }}>
                                    <label class="form-check-label" for="small_business_no">No</label>
                                </div>
                            </div>
                            <div class="error-message text-danger mt-1" style="display: none;"></div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Step 2: Social Media -->
        <div class="onboarding-step-content d-none" data-step="social-media">
            <h2 class="mb-4">Social Media Platforms</h2>
            <p class="text-muted mb-4">Connect your social media accounts to showcase your reach and engagement.</p>

            <div class="social-connect-container">
                @include('front-user.pages.influencer-socialconnect')
            </div>
        </div>

        <!-- Step 3: General Information -->
        <div class="onboarding-step-content d-none" data-step="general-information">
            <h2 class="mb-4">General Information</h2>

            <form action="{{ url('/save-onboarding-step') }}" method="post" class="onboarding-form" data-parsley-validate>
                @csrf
                <input type="hidden" name="step" value="general-information">
                @include('front-user.pages.general-information')
            </form>
        </div>

        <!-- Step 4: Target Group -->
        <div class="onboarding-step-content d-none" data-step="target-group">
            <h2 class="mb-4">Select Target Group</h2>

            <form action="{{ url('/save-onboarding-step') }}" method="post" class="onboarding-form" data-parsley-validate>
                @csrf
                <input type="hidden" name="step" value="target-group">
                @include('front-user.pages.target-group')
            </form>
        </div>

        <!-- Step 5: Campaign Types -->
        <div class="onboarding-step-content d-none" data-step="campaign-types">
            <h2 class="mb-4">Campaign Types</h2>
            <div class="alert alert-info mb-4">
                <i class="fas fa-info-circle me-2"></i>
                Brands can request you only for the activated campaign types
            </div>

            <form action="{{ url('/save-onboarding-step') }}" method="post" class="onboarding-form" data-parsley-validate>
                @csrf
                <input type="hidden" name="step" value="campaign-types">
                <input type="hidden" name="advertising_method" value="true">
                @include('front-user.pages.influencer-advertising-choose')
            </form>
        </div>
            <div class="steps_con" id="collection5">
                <div class="mobile-step-detail">
                    <div class="steps-cont">STEP 06</div>
                    <div class="steps-which">Go Live</div>
                </div>
                @if ($currentUser->activate == '2')
                    <form action="{{ url('/save-influencer-form') }}" method="post" data-parsley-validate id="draft4">
                        @csrf
                        <input type="hidden" name="advertising_method_price" value="true">
                        <div class="informationDiv advertisingPriceMethod1">
                            @include('front-user.pages.influencer-pricing-choose')
                        </div>
                    </form>
                @else
                    <form action="{{ url('/save-influencer-form') }}" method="post" id="draft5">
                        @csrf
                        <div class="informationDiv">
                            <ul class="nav nav-tabs" id="myTab" role="tablist"></ul>
                            <div class="tab-content connectPublish" id="myTabContent">
                                @include('front-user.pages.influencer-publish')
                                <div class="offset-3 col-6 text-center lets_go_btn">
                                    <input type="hidden" name="publish" value="Publish">
                                    <input type="hidden" name="Publishonline" value="PublishOnline">
                                    <button type="submit" class="button-ccg new-style newpost float-none publish" name="PublishOnline" value="PublishOnline">Let’s go online!</button>
                                </div>
                            </div>
                            <div class="step-nevigationbutton">
                                <div class="nav-left back me-2" id="Back_step4">
                                    <img src="{{ asset('assets/front-end/images/icons/step-left.svg') }}" alt="">
                                </div>
                                <div class="nav-right next ms-auto">
                                    <img src="{{ asset('assets/front-end/images/icons/step-right.svg') }}" alt="" style="background: #d5cbe9 !important; cursor: default;">
                                </div>
                            </div>
                        </div>
                    </form>
                @endif
            </div>

        </div>
    </div>
</section>

<!-- Social connect error popup Start -->
<div class="modal fade influncer" id="payouterrorpopup" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="payoutconfirmpopupLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <div class="text-center errorBox">
                    <div class="text-center chkImage">
                        <img src="{{ asset('/assets/front-end/images/icons/icon-no-data-image.png') }}" alt="">
                    </div>
                    <div class="ortContentntr" id="errorText">Something went wrong!</div>
                    <button class="btnpor bg-error color-white mb-5" type="button" data-bs-dismiss="modal">Ok</button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script_codes')
<!-- Include the new onboarding JavaScript -->
<script src="{{ asset('assets/front-end/js/influencer-onboarding.js') }}"></script>

<script>
    // Legacy compatibility - can be removed once fully tested
    $(document).ready(function() {
        // Initialize Parsley validation if available
        if (typeof $.fn.parsley !== 'undefined') {
            $('.onboarding-form').parsley();
        }

        // Initialize Select2 if available
        if (typeof $.fn.select2 !== 'undefined') {
            $('.form-select').select2();
        }
    });

    function saveDraft(count) {
        $('#draftcount').val(count);
        $(".publishButton").each(function() {
            var star1 = $(this);
            star1.val('Draft');
        });

        var hashtag1 = safeVal("#hashtag1");
        var hashtag2 = safeVal("#hashtag2");
        var hashtag3 = safeVal("#hashtag3");

        if (hashtag1 !== "" || hashtag2 !== "" || hashtag3 !== "") {
            if (hashtag1 === hashtag2 || hashtag1 === hashtag3 || hashtag2 === hashtag3) {
                $(".hashtagerror").show();
                return false;
            } else {
                $(".hashtagerror").hide();
            }
        }

        if (document.getElementById('user_status') && document.getElementById('user_status').checked) {
            $('#draftmode').modal('show');
        } else {
            $('#draft' + count).submit();
        }
    }

    if (safeVal("#stripe_status") == "not_connected") {
        $(".ser_op").addClass("disabled");
    }

    $(document).on("click", ".next", function(event) {
        if (safeVal("#stripe_status") == "connected") {
            const str = undefined;
            const checkaltr = $(this).closest(".steps_con").attr("data-content").split("collection");

            $(".content_language").each(function() {
                var star1 = $(this);
                star1.html(safeVal('select[name="content_language"]'));
            });
            $(".ages").each(function() {
                var star2 = $(this);
                star2.html(safeVal('select[name="ages"]'));
            });
            $(".content_attracts").each(function() {
                var star3 = $(this);
                star3.text(safeVal('input[name="content_attracts"]:checked'));
            });

            $('.infoPrice_youtube').html(safeVal('input[name="video_price_youtube"]'));
            $('.infoPrice_twitter').html(safeVal('input[name="video_price_twitter"]'));
            $('.infoPrice_twitch').html(safeVal('input[name="video_price_twitch"]'));
            $('.infoPrice_facebook').html(safeVal('input[name="video_price_facebook"]'));
            $('.infoPrice_instagram').html(safeVal('input[name="video_price_instagram"]'));

            $('.liveStream_youtube').html(safeVal('input[name="livestream_price_youtube"]'));
            $('.liveStream_twitter').html(safeVal('input[name="livestream_price_twitter"]'));
            $('.liveStream_twitch').html(safeVal('input[name="livestream_price_twitch"]'));
            $('.liveStream_facebook').html(safeVal('input[name="livestream_price_facebook"]'));
            $('.liveStream_instagram').html(safeVal('input[name="livestream_price_instagram"]'));

            $('.share_youtube').html(safeVal('input[name="share_price_youtube"]'));
            $('.share_twitter').html(safeVal('input[name="share_price_twitter"]'));
            $('.share_twitch').html(safeVal('input[name="share_price_twitch"]'));
            $('.share_facebook').html(safeVal('input[name="share_price_facebook"]'));
            $('.share_instagram').html(safeVal('input[name="share_price_instagram"]'));

            var target = $(this).closest('.steps_con').next('.steps_con').attr("data-content");
            var mainTarget = $(this).closest('.steps_con').attr("data-content");
            var slic = $(this).closest('.steps_con').next('.steps_con').attr("data-content").replace(/collection/, '');

            if (mainTarget != 'collection1') {
                $(this).closest('.steps_con').find("form").each(function() {
                    $(this).closest('.steps_con').find("form").parsley().validate();
                    if ($(this).closest('.steps_con').find("form").parsley().isValid()) {
                        $('#selectcategory1').parsley().validate();
                        var formData = null;
                        if (mainTarget == 'collection0') {
                            formData = $('#draft0').serialize();
                            $.ajax({
                                url: "{{ url('/my-profile') }}",
                                type: "post",
                                dataType: 'json',
                                data: formData,
                            }).done(function(data) {
                                // Profile saved successfully
                            });
                        } else if (mainTarget == 'collection2') {
                            var hashtag1 = safeVal("#hashtag1");
                            var hashtag2 = safeVal("#hashtag2");
                            var hashtag3 = safeVal("#hashtag3");

                            if (hashtag1 === hashtag2 || hashtag1 === hashtag3 || hashtag2 === hashtag3) {
                                $(".hashtagerror").show();
                                return false;
                            } else {
                                $(".hashtagerror").hide();
                            }
                            formData = $('#draft1').serialize();
                        } else if (mainTarget == 'collection3') {
                            formData = $('#draft2').serialize();
                        } else if (mainTarget == 'collection4') {
                            formData = $('#draft3').serialize();
                        } else if (mainTarget == 'collection5') {
                            formData = $('#draft4').serialize();
                        }

                        if (formData != null) {
                            $.ajax({
                                url: "{{ url('/save-influencer-form') }}",
                                type: "post",
                                dataType: 'json',
                                data: formData,
                            }).done(function(data) {
                                if (data.status == true) {
                                    $('.generalInfo').html(data.generalPage);
                                    $(".targetGroup").html(data.targetPage);
                                    $(".advertisingMethod1").html(data.advertisingPage);
                                    $(".advertisingPriceMethod1").html(data.advertisingPricePage);
                                    $(".connectPublish").html(data.publishPage);

                                    if (safeVal("select[name=sharecontent_media]") == "") {
                                        $("select[name=sharecontent_media]").closest(".select_media").find("input[type=checkbox]").prop('checked', false);
                                        $("select[name=sharecontent_media]").prop('disabled', true);
                                        $("select[name=sharecontent_media]").closest(".select_media").removeClass("checkedmedia");
                                    }

                                    if (safeVal("select[name=video_media]") == "") {
                                        $("select[name=video_media]").closest(".select_media").find("input[type=checkbox]").prop('checked', false);
                                        $("select[name=video_media]").prop('disabled', true);
                                        $("select[name=video_media]").closest(".select_media").removeClass("checkedmedia");
                                    }

                                    if (safeVal("select[name=livestream_media]") == "") {
                                        $("select[name=livestream_media]").closest(".select_media").find("input[type=checkbox]").prop('checked', false);
                                        $("select[name=livestream_media]").prop('disabled', true);
                                        $("select[name=livestream_media]").closest(".select_media").removeClass("checkedmedia");
                                    }

                                    $(".select").select2();
                                    $('div[data-title=' + target + ']').removeClass("disabled");
                                    var isValid = true;
                                    $('div[data-content=' + target + '] .form-control[required]').each(function() {
                                        if ($(this).val() === '') {
                                            isValid = false;
                                            event.stopPropagation();
                                        }
                                    });

                                    $("input.classtop").change(function() {
                                        var valueName = $(this).val();
                                        if ($(this).is(':checked')) {
                                            if ($(this).val() == "video_on_demand") {
                                                $(".socialMediaSection input[data-id= video_on_demand]").attr("disabled", false);
                                            } else if ($(this).val() == "livestream") {
                                                $(".socialMediaSection input[data-id=livestream]").attr("disabled", false);
                                            } else if ($(this).val() == "shareContent") {
                                                $(".socialMediaSection input[data-id= shareContent]").attr("disabled", false);
                                            }
                                            $(this).closest(".select_media").addClass("checkedmedia");
                                        } else {
                                            $(".socialMediaSection input[data-id=" + valueName + "]").attr("disabled", true).attr("checked", false);
                                            $(this).closest(".select_media").removeClass("checkedmedia snapchat instagram twitter tiktok youtube facebook twitch");
                                        }
                                    });

                                    var media = $('input[name="media"]:checked').val();
                                    var type_count = @json($user);

                                    var media = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                    var type = 'Boost me';
                                    $.ajax({
                                        url: "{{ url('get-admin-pricing') }}",
                                        data: {
                                            media: media,
                                            type: type
                                        }
                                    }).done(function(data) {
                                        if (data.price != null) {
                                            $(".type_price_boost").val(data.price);
                                            $(".type_price_boost_text").text(data.price);
                                            $(".type_price_boost_next").val(data.price_next);
                                            $(".type_price_boost_next_text").text(data.price_next);

                                            var per = ((data.price_next / data.price) * 100) - 100;
                                            const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                            $(".price-current_boost_per").text(roundedPer);
                                        } else {
                                            $(".type_price_boost").val('');
                                            $(".type_price_boost_text").text('');
                                            $(".type_price_boost_next").val('');
                                            $(".type_price_boost_next_text").text('');
                                            $(".price-current_boost_per").text('0');
                                        }
                                    });

                                    var media1 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                    var type1 = 'Reaction video';
                                    $.ajax({
                                        url: "{{ url('get-admin-pricing') }}",
                                        data: {
                                            media: media1,
                                            type: type1
                                        }
                                    }).done(function(data) {
                                        if (data.price != null) {
                                            $(".type_price_reaction").val(data.price);
                                            $(".type_price_reaction_text").text(data.price);
                                            $(".type_price_reaction_next").val(data.price_next);
                                            $(".type_price_reaction_next_text").text(data.price_next);

                                            var per = ((data.price_next / data.price) * 100) - 100;
                                            const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                            $(".price-current_reaction_per").text(roundedPer);
                                        } else {
                                            $(".type_price_reaction").val('');
                                            $(".type_price_reaction_text").text('');
                                            $(".type_price_reaction_next").val('');
                                            $(".type_price_reaction_next_text").text('');
                                            $(".price-current_reaction_per").text('0');
                                        }
                                    });

                                    var media2 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                    var type2 = 'Survey';
                                    $.ajax({
                                        url: "{{ url('get-admin-pricing') }}",
                                        data: {
                                            media: media2,
                                            type: type2
                                        }
                                    }).done(function(data) {
                                        if (data.price != null) {
                                            $(".type_price_survey").val(data.price);
                                            $(".type_price_survey_text").text(data.price);
                                            $(".type_price_survey_next").val(data.price_next);
                                            $(".type_price_survey_next_text").text(data.price_next);

                                            var per = ((data.price_next / data.price) * 100) - 100;
                                            const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                            $(".price-current_survey_per").text(roundedPer);
                                        } else {
                                            $(".type_price_survey").val('');
                                            $(".type_price_survey_text").text('');
                                            $(".type_price_survey_next").val('');
                                            $(".type_price_survey_next_text").text('');
                                            $(".price-current_survey_per").text('0');
                                        }
                                    });

                                    $(".campaign-type-content input[type=checkbox]").each(function() {
                                        var media = $(".campaign-type-content input").attr("data-media");
                                        if ($(this).is(':checked')) {
                                            var media = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                            var type = 'Boost me';
                                            $.ajax({
                                                url: "{{ url('get-admin-pricing') }}",
                                                data: {
                                                    media: media,
                                                    type: type
                                                }
                                            }).done(function(data) {
                                                if (data.price != null) {
                                                    $(".type_price_boost").val(data.price);
                                                    $(".type_price_boost_text").text(data.price);
                                                    $(".type_price_boost_next").val(data.price_next);
                                                    $(".type_price_boost_next_text").text(data.price_next);

                                                    var per = ((data.price_next / data.price) * 100) - 100;
                                                    const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                                    $(".price-current_boost_per").text(roundedPer);
                                                } else {
                                                    $(".type_price_boost").val('');
                                                    $(".type_price_boost_text").text('');
                                                    $(".type_price_boost_next").val('');
                                                    $(".type_price_boost_next_text").text('');
                                                    $(".price-current_boost_per").text('0');
                                                }
                                            });

                                            var media1 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                            var type1 = 'Reaction video';
                                            $.ajax({
                                                url: "{{ url('get-admin-pricing') }}",
                                                data: {
                                                    media: media1,
                                                    type: type1
                                                }
                                            }).done(function(data) {
                                                if (data.price != null) {
                                                    $(".type_price_reaction").val(data.price);
                                                    $(".type_price_reaction_text").text(data.price);
                                                    $(".type_price_reaction_next").val(data.price_next);
                                                    $(".type_price_reaction_next_text").text(data.price_next);

                                                    var per = ((data.price_next / data.price) * 100) - 100;
                                                    const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                                    $(".price-current_reaction_per").text(roundedPer);
                                                } else {
                                                    $(".type_price_reaction").val('');
                                                    $(".type_price_reaction_text").text('');
                                                    $(".type_price_reaction_next").val('');
                                                    $(".type_price_reaction_next_text").text('');
                                                    $(".price-current_reaction_per").text('0');
                                                }
                                            });

                                            var media2 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                            var type2 = 'Survey';
                                            $.ajax({
                                                url: "{{ url('get-admin-pricing') }}",
                                                data: {
                                                    media: media2,
                                                    type: type2
                                                }
                                            }).done(function(data) {
                                                if (data.price != null) {
                                                    $(".type_price_survey").val(data.price);
                                                    $(".type_price_survey_text").text(data.price);
                                                    $(".type_price_survey_next").val(data.price_next);
                                                    $(".type_price_survey_next_text").text(data.price_next);

                                                    var per = ((data.price_next / data.price) * 100) - 100;
                                                    const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                                    $(".price-current_survey_per").text(roundedPer);
                                                } else {
                                                    $(".type_price_survey").val('');
                                                    $(".type_price_survey_text").text('');
                                                    $(".type_price_survey_next").val('');
                                                    $(".type_price_survey_next_text").text('');
                                                    $(".price-current_survey_per").text('0');
                                                }
                                            });
                                        }

                                        if ($(this).prop('checked') == false) {
                                            $(this).closest(".d-flex").find(".react-action-price-one").addClass("disabled-input");
                                            $(this).closest(".d-flex").find(".react-action-price-one").find("input").val("");
                                            $(this).closest(".d-flex").find(".react-action-price-one.disable").find("input").val("");
                                        }
                                    });
                                }

                                $(document).on('change', '.shareconent', function() {
                                    var mediaLink = $(this).val();
                                    $(this).closest(".select_media ").addClass(mediaLink);

                                    if ($(this).val() == "facebook") {
                                        $(this).closest(".select_media ").removeClass("instagram twitter tiktok youtube snapchat twitch");
                                    } else if ($(this).val() == "twitch") {
                                        $(this).closest(".select_media ").removeClass("instagram facebook twitter tiktok youtube snapchat");
                                    } else if ($(this).val() == "instagram") {
                                        $(this).closest(".select_media ").removeClass("facebook twitter tiktok youtube snapchat twitch");
                                    } else if ($(this).val() == "twitter") {
                                        $(this).closest(".select_media ").removeClass("instagram facebook tiktok youtube snapchat twitch");
                                    } else if ($(this).val() == "tiktok") {
                                        $(this).closest(".select_media ").removeClass("instagram twitter facebook youtube snapchat twitch");
                                    } else if ($(this).val() == "youtube") {
                                        $(this).closest(".select_media ").removeClass("instagram twitter tiktok facebook snapchat twitch");
                                    } else if ($(this).val() == "snapchat") {
                                        $(this).closest(".select_media ").removeClass("instagram twitter tiktok youtube facebook twitch");
                                    }
                                });

                                $('.shareconent').each(function() {
                                    var mediaLink = $(this).val();
                                    $(this).closest(".select_media ").addClass(mediaLink);
                                });
                            });
                        }

                        $(this).closest('.steps_con').hide();
                        $(".ser_op").removeClass("current");
                        if (target == "collection1") {
                            @if(isset($social_connect_media) && $social_connect_media->count() > 0)
                                $(".ser_op[data-title=" + target + "]").addClass("current").removeClass("disabled");
                            @else
                                $(".ser_op[data-title=" + target + "]").addClass("current");
                            @endif
                        } else {
                            $(".ser_op[data-title=" + target + "]").addClass("current").removeClass("disabled");
                        }

                        $(".steps_con[data-content=" + target + "]").show();
                    } else {
                        $(this).closest("form").submit();
                    }
                });
            } else {
                if ($("#collection1 .connectWith .connectWithInner").hasClass("disconnectedToSocial")) {
                    $(this).closest('.steps_con').hide();
                    $(".ser_op").removeClass("current");
                    $(".ser_op[data-title=" + target + "]").addClass("current").removeClass("disabled");
                    $(".steps_con[data-content=" + target + "]").show();
                } else {
                    $('#errorText').html('<div class="worning_point">Please connect at least one social media to proceed</div>');
                    $("#collection1 .next").addClass("disabled");
                }
            }
        } else if (safeVal("#stripe_status") == "not_connected") {
            $('#stripeError').html('<div class="worning_point">Please connect with Stripe before proceeding to the next step</div>');
            $(".ser_op").addClass("disabled");
            $('html, body').animate({ scrollTop: 0 }, 'slow');
        }
    });

    $(document).on("change", ".campaign-type-content input", function() {
        var media = $(this).attr("data-media");
        if ($(this).attr("name", "type_reaction").is(':checked')) {
            var media1 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
            var type1 = 'Reaction video';
            $.ajax({
                    url: "{{ url('get-admin-pricing') }}",
                    data: {
                        media: media1,
                        type: type1
                    }
                })
                .done(function(data) {
                    if (data.price != null) {
                        $(".type_price_reaction").val(data.price);
                        $(".type_price_reaction_text").text(data.price);
                        $(".type_price_reaction_next").val(data.price_next);
                        $(".type_price_reaction_next_text").text(data.price_next);

                        var per = ((data.price_next / data.price) * 100) - 100;
                        const roundedPer = data.price > 0 ? Math.round(per) : 0;
                        $(".price-current_reaction_per").text(roundedPer);
                    } else {
                        $(".type_price_reaction").val('');
                        $(".type_price_reaction_text").text('');
                        $(".type_price_reaction_next").val('');
                        $(".type_price_reaction_next_text").text('');
                        $(".price-current_reaction_per").text('0');
                    }
                });

        }

        if ($(this).attr("name", "type_boost").is(':checked')) {
            var media = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
            var type = 'Boost me';
            $.ajax({
                    url: "{{ url('get-admin-pricing') }}",
                    data: {
                        media: media,
                        type: type
                    }
                })
                .done(function(data) {
                    if (data.price != null) {
                        $(".type_price_boost").val(data.price);
                        $(".type_price_boost_text").text(data.price);
                        $(".type_price_boost_next").val(data.price_next);
                        $(".type_price_boost_next_text").text(data.price_next);

                        var per = ((data.price_next / data.price) * 100) - 100;
                        const roundedPer = data.price > 0 ? Math.round(per) : 0;
                        $(".price-current_boost_per").text(roundedPer);
                    } else {
                        $(".type_price_boost").val('');
                        $(".type_price_boost_text").text('');
                        $(".type_price_boost_next").val('');
                        $(".type_price_boost_next_text").text('');
                        $(".price-current_boost_per").text('0');
                    }
                });
        }

        if ($(this).attr("name", "type_survey").is(':checked')) {
            var media2 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
            var type2 = 'Survey';
            $.ajax({
                    url: "{{ url('get-admin-pricing') }}",
                    data: {
                        media: media2,
                        type: type2
                    }
                })
                .done(function(data) {
                    if (data.price != null) {
                        $(".type_price_survey").val(data.price);
                        $(".type_price_survey_text").text(data.price);
                        $(".type_price_survey_next").val(data.price_next);
                        $(".type_price_survey_next_text").text(data.price_next);

                        var per = ((data.price_next / data.price) * 100) - 100;
                        const roundedPer = data.price > 0 ? Math.round(per) : 0;
                        $(".price-current_survey_per").text(roundedPer);
                    } else {
                        $(".type_price_survey").val('');
                        $(".type_price_survey_text").text('');
                        $(".type_price_survey_next").val('');
                        $(".type_price_survey_next_text").text('');
                        $(".price-current_survey_per").text('0');
                    }
                });

        }
    })

    $("input.classtop").change(function() {
        var valueName = $(this).val();
        if ($(this).is(':checked')) {
            if ($(this).val() == "video_on_demand") {
                $(".socialMediaSection input[data-id= video_on_demand]").attr("disabled", false);
            } else if ($(this).val() == "livestream") {
                $(".socialMediaSection input[data-id=livestream]").attr("disabled", false);
            } else if ($(this).val() == "shareContent") {
                $(".socialMediaSection input[data-id= shareContent]").attr("disabled", false);
            }

            $(this).closest(".select_media").addClass("checkedmedia");
        } else {
            $(".socialMediaSection input[data-id=" + valueName + "]").attr("disabled", true).attr("checked", false);
            $(this).closest(".select_media").removeClass("checkedmedia snapchat instagram twitter tiktok youtube facebook twitch");
        }

    });

    $('input.classtop').each(function() {
        var valueName = $(this).val();
        if ($(this).is(':checked')) {
            if ($(this).val() == "video_on_demand") {
                $(".socialMediaSection input[data-id= video_on_demand]").attr("disabled", false);

            } else if ($(this).val() == "livestream") {
                $(".socialMediaSection input[data-id=livestream]").attr("disabled", false);


            } else if ($(this).val() == "shareContent") {
                $(".socialMediaSection input[data-id= shareContent]").attr("disabled", false);

            }
            $(this).closest(".select_media").addClass("checkedmedia");
        } else {
            $(".socialMediaSection input[data-id=" + valueName + "]").attr("disabled", true).attr("checked",
                false);
            $(this).closest(".select_media").removeClass(
                "checkedmedia snapchat instagram twitter tiktok youtube facebook twitch");
        }
    });
    $(".shareconent").change(function() {
        $(this).closest(".select_media").removeClass(
            "snapchat instagram twitter tiktok youtube facebook twitch");
        var valueName = $(this).val();
        $(this).closest(".select_media").addClass(valueName);
    });

    $('.shareconent').each(function() {
        if ($(this).val() == "") {
            $(this).closest(".select_media").removeClass(
                "snapchat instagram twitter tiktok youtube facebook twitch");
        }
    });

    function formatState(state) {
        if (!state.id) {
            return state.text;
        }
        var baseUrl = "{{ asset('/assets/front-end/images') }}";
        var $state = $(
            '<span><img src="' + baseUrl + '/col_icon_' + state.element.value.toLowerCase() +
            '.png" class="img-flag" /> ' + state.text + '</span>'
        );
        return $state;
    };

    $("form input, form select").change(function() {
        var form_id = $(this).closest(".steps_con").attr("data-content");
        $("#formId").val(form_id);
    });

    $(document).on("click", ".ser_op", function(e) {
        var mainTarget = $(".ser_op.current").attr("data-title");
        var dataContent = $(this).attr("data-title").replace(/collection/, '');
        if (mainTarget != 'collection1') {
            $("#"+mainTarget).find("form").each(function(){
                $("#"+mainTarget).find("form").parsley().validate()
                if ($("#"+mainTarget).find("form").parsley().isValid()) {
                    $(".ser_op").removeClass("current");
                    $("[data-title=collection"+dataContent+"]").addClass("current");
                    $(".steps_con").removeClass("formActive").hide();
                    $("#collection"+dataContent).addClass("formActive").show();
                    if (mainTarget == 'collection0') {
                        formData = $('#draft0').serialize();
                        $.ajax({
                            url: "{{ url('/my-profile') }}",
                            type: "post",
                            dataType: 'json',
                            data: formData,
                        }).done(function(data) {
                            // Profile saved successfully
                        })
                    }else if (mainTarget == 'collection2') {
                        var hashtag1 = safeVal("#hashtag1");
                        var hashtag2 = safeVal("#hashtag2");
                        var hashtag3 = safeVal("#hashtag3");

                        if (hashtag1 === hashtag2 || hashtag1 === hashtag3 || hashtag2 === hashtag3) {
                            $(".hashtagerror").show();
                            return false;
                        }else {
                            $(".hashtagerror").hide();
                        }
                        formData = $('#draft1').serialize();
                    } else if (mainTarget == 'collection3') {
                        formData = $('#draft2').serialize();
                    } else if (mainTarget == 'collection4') {
                        formData = $('#draft3').serialize();

                    } else if (mainTarget == 'collection5') {
                        formData = $('#draft4').serialize();
                    }
                    if (formData != null) {
                        $.ajax({
                            url: "{{ url('/save-influencer-form') }}",
                            type: "post",
                            dataType: 'json',
                            data: formData,
                        }).done(function(data) {
                            if (data.status == true) {
                                if($("#collection2").find('form').data('changed')) {
                                    //do something
                                    $('.generalInfo').html(data.generalPage);
                                }
                                $(".targetGroup").html(data.targetPage);
                                $(".advertisingMethod1").html(data.advertisingPage);
                                $(".advertisingPriceMethod1").html(data.advertisingPricePage);
                                $(".connectPublish").html(data.publishPage);
                                if ($("select[name=sharecontent_media]").val() == "") {
                                    $("select[name=sharecontent_media]").closest(".select_media").find(
                                        "input[type=checkbox]").prop('checked', false);
                                    $("select[name=sharecontent_media]").prop('disabled', true);
                                    $("select[name=sharecontent_media]").closest(".select_media")
                                        .removeClass("checkedmedia")
                                }
                                if ($("select[name=video_media]").val() == "") {
                                    $("select[name=video_media]").closest(".select_media").find(
                                        "input[type=checkbox]").prop('checked', false);
                                    $("select[name=video_media]").prop('disabled', true);
                                    $("select[name=video_media]").closest(".select_media").removeClass(
                                        "checkedmedia")
                                }
                                if ($("select[name=livestream_media]").val() == "") {
                                    $("select[name=livestream_media]").closest(".select_media").find(
                                        "input[type=checkbox]").prop('checked', false);
                                    $("select[name=livestream_media]").prop('disabled', true);
                                    $("select[name=livestream_media]").closest(".select_media")
                                        .removeClass("checkedmedia")
                                }

                                $(".select").select2();
                                $('div[data-title=' + mainTarget + ']').removeClass("disabled");
                                var isValid = true;
                                $('div[data-content=' + mainTarget + '] .form-control[required]').each(
                                function() {
                                    if ($(this).val() === '') {
                                        isValid = false;
                                        event.stopPropagation();
                                    }
                                });

                                $("input.classtop").change(function() {
                                    var valueName = $(this).val();
                                    if ($(this).is(':checked')) {
                                        if ($(this).val() == "video_on_demand") {
                                            $(".socialMediaSection input[data-id= video_on_demand]")
                                                .attr("disabled", false);

                                        } else if ($(this).val() == "livestream") {
                                            $(".socialMediaSection input[data-id=livestream]")
                                                .attr("disabled", false);


                                        } else if ($(this).val() == "shareContent") {
                                            $(".socialMediaSection input[data-id= shareContent]")
                                                .attr("disabled", false);

                                        }
                                        $(this).closest(".select_media").addClass(
                                            "checkedmedia");
                                    } else {
                                        $(".socialMediaSection input[data-id=" + valueName +
                                            "]").attr("disabled", true).attr("checked",
                                            false);
                                        $(this).closest(".select_media").removeClass(
                                            "checkedmedia snapchat instagram twitter tiktok youtube facebook twitch"
                                            );
                                    }

                                });

                                var media = $('input[name="media"]:checked').val();
                                var type_count = @json($user);

                                var media = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                var type = 'Boost me';
                                $.ajax({
                                        url: "{{ url('get-admin-pricing') }}",
                                        data: {
                                            media: media,
                                            type: type
                                        }
                                    })
                                    .done(function(data) {
                                        if (data.price != null) {
                                            $(".type_price_boost").val(data.price);
                                            $(".type_price_boost_text").text(data.price);
                                            $(".type_price_boost_next").val(data.price_next);
                                            $(".type_price_boost_next_text").text(data.price_next);

                                            var per = ( (data.price_next / data.price) * 100) - 100;
                                            const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                            $(".price-current_boost_per").text(roundedPer);
                                        } else {
                                            $(".type_price_boost").val('');
                                            $(".type_price_boost_text").text('');
                                            $(".type_price_boost_next").val('');
                                            $(".type_price_boost_next_text").text('');
                                            $(".price-current_boost_per").text('0');
                                        }
                                    });


                                var media1 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                var type1 = 'Reaction video';
                                $.ajax({
                                    url: "{{ url('get-admin-pricing') }}",
                                    data: {
                                        media: media1,
                                        type: type1
                                    }
                                })
                                .done(function(data) {
                                    if (data.price != null) {
                                        $(".type_price_reaction").val(data.price);
                                        $(".type_price_reaction_text").text(data.price);
                                        $(".type_price_reaction_next").val(data.price_next);
                                        $(".type_price_reaction_next_text").text(data.price_next);

                                        var per = ((data.price_next / data.price) * 100) - 100;
                                        const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                        $(".price-current_reaction_per").text(roundedPer);
                                    } else {
                                        $(".type_price_reaction").val('');
                                        $(".type_price_reaction_text").text('');
                                        $(".type_price_reaction_next").val('');
                                        $(".type_price_reaction_next_text").text('');
                                        $(".price-current_reaction_per").text('0');
                                    }
                                });



                                var media2 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                var type2 = 'Survey';
                                $.ajax({
                                    url: "{{ url('get-admin-pricing') }}",
                                    data: {
                                        media: media2,
                                        type: type2
                                    }
                                })
                                .done(function(data) {
                                    if (data.price != null) {
                                        $(".type_price_survey").val(data.price);
                                        $(".type_price_survey_text").text(data.price);
                                        $(".type_price_survey_next").val(data.price_next);
                                        $(".type_price_survey_next_text").text(data.price_next);

                                        var per = ((data.price_next / data.price) * 100) - 100;
                                        const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                        $(".price-current_survey_per").text(roundedPer);
                                    } else {
                                        $(".type_price_survey").val('');
                                        $(".type_price_survey_text").text('');
                                        $(".type_price_survey_next").val('');
                                        $(".type_price_survey_next_text").text('');
                                        $(".price-current_survey_per").text('0');
                                    }
                                });


                                $(".campaign-type-content input[type=checkbox]").each(function() {
                                    var media = $(".campaign-type-content input").attr(
                                        "data-media");
                                    if ($(this).is(':checked')) {


                                        var media = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                        var type = 'Boost me';
                                        $.ajax({
                                                url: "{{ url('get-admin-pricing') }}",
                                                data: {
                                                    media: media,
                                                    type: type
                                                }
                                            })
                                            .done(function(data) {
                                                if (data.price != null) {
                                                    $(".type_price_boost").val(data.price);
                                                    $(".type_price_boost_text").text(data.price);
                                                    $(".type_price_boost_next").val(data.price_next);
                                                    $(".type_price_boost_next_text").text(data.price_next);

                                                    var per = ((data.price_next / data.price) * 100) - 100;
                                                    const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                                    $(".price-current_boost_per").text(roundedPer);
                                                } else {
                                                    $(".type_price_boost").val('');
                                                    $(".type_price_boost_text").text('');
                                                    $(".type_price_boost_next").val('');
                                                    $(".type_price_boost_next_text").text(
                                                        '');
                                                    $(".price-current_boost_per").text('0');
                                                }
                                            });


                                        var media1 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                        var type1 = 'Reaction video';
                                        $.ajax({
                                                url: "{{ url('get-admin-pricing') }}",
                                                data: {
                                                    media: media1,
                                                    type: type1
                                                }
                                            })
                                            .done(function(data) {
                                                if (data.price != null) {
                                                    $(".type_price_reaction").val(data.price);
                                                    $(".type_price_reaction_text").text(data.price);
                                                    $(".type_price_reaction_next").val(data.price_next);
                                                    $(".type_price_reaction_next_text") .text(data.price_next);

                                                    var per = ((data.price_next / data.price) * 100) - 100;
                                                    const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                                    $(".price-current_reaction_per").text(roundedPer);
                                                } else {
                                                    $(".type_price_reaction").val('');
                                                    $(".type_price_reaction_text").text('');
                                                    $(".type_price_reaction_next").val('');
                                                    $(".type_price_reaction_next_text").text('');
                                                    $(".price-current_reaction_per").text('0');
                                                }
                                            });



                                        var media2 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                        var type2 = 'Survey';
                                        $.ajax({
                                                url: "{{ url('get-admin-pricing') }}",
                                                data: {
                                                    media: media2,
                                                    type: type2
                                                }
                                            })
                                            .done(function(data) {
                                                if (data.price != null) {
                                                    $(".type_price_survey").val(data.price);
                                                    $(".type_price_survey_text").text(data.price);
                                                    $(".type_price_survey_next").val(data.price_next);
                                                    $(".type_price_survey_next_text").text(data.price_next);

                                                    var per = ((data.price_next / data.price) * 100) - 100;
                                                    const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                                    $(".price-current_survey_per").text(roundedPer);
                                                } else {
                                                    $(".type_price_survey").val('');
                                                    $(".type_price_survey_text").text('');
                                                    $(".type_price_survey_next").val('');
                                                    $(".type_price_survey_next_text").text(
                                                        '');
                                                    $(".price-current_survey_per").text('0');
                                                }
                                            });

                                    }
                                    if ($(this).prop('checked') == false) {
                                        $(this).closest(".d-flex").find(
                                            ".react-action-price-one").addClass(
                                            "disabled-input");
                                        $(this).closest(".d-flex").find(
                                            ".react-action-price-one").find("input").val("")
                                        $(this).closest(".d-flex").find(
                                                ".react-action-price-one.disable").find("input")
                                            .val("")
                                    }
                                })


                            }
                            $(document).on('change', '.shareconent', function() {
                                var mediaLink = $(this).val();
                                $(this).closest(".select_media ").addClass(mediaLink);

                                if ($(this).val() == "facebook") {
                                    $(this).closest(".select_media ").removeClass(
                                        "instagram twitter tiktok youtube snapchat twitch")
                                } else if ($(this).val() == "twitch") {
                                    $(this).closest(".select_media ").removeClass(
                                        "instagram facebook twitter tiktok youtube snapchat"
                                        )
                                } else if ($(this).val() == "instagram") {
                                    $(this).closest(".select_media ").removeClass(
                                        "facebook twitter tiktok youtube snapchat twitch")
                                } else if ($(this).val() == "twitter") {
                                    $(this).closest(".select_media ").removeClass(
                                        "instagram facebook tiktok youtube snapchat twitch")
                                } else if ($(this).val() == "tiktok") {
                                    $(this).closest(".select_media ").removeClass(
                                        "instagram twitter facebook youtube snapchat twitch"
                                        )
                                } else if ($(this).val() == "youtube") {
                                    $(this).closest(".select_media ").removeClass(
                                        "instagram twitter tiktok facebook snapchat twitch")
                                } else if ($(this).val() == "snapchat") {
                                    $(this).closest(".select_media ").removeClass(
                                        "instagram twitter tiktok youtube facebook twitch")
                                }
                            });
                            $('.shareconent').each(function() {
                                var mediaLink = $(this).val();
                                $(this).closest(".select_media ").addClass(mediaLink);
                            });
                        });

                    }
                }
            })
        }else{
            $(".ser_op").removeClass("current");
            $("[data-title=collection"+dataContent+"]").addClass("current");
            $(".steps_con").removeClass("formActive").hide();
            $("#collection"+dataContent).addClass("formActive").show();
        }
    });

    $(document).on("click", "#draft1 [type=submit]", function(e) {
        var hashtag1 = safeVal("#hashtag1");
        var hashtag2 = safeVal("#hashtag2");
        var hashtag3 = safeVal("#hashtag3");

        if (hashtag1 === hashtag2 || hashtag1 === hashtag3 || hashtag2 === hashtag3) {
            $(".hashtagerror").show();
            e.preventDefault();
            return false;
        } else {
            $(".hashtagerror").hide();
        }

    })
    $("#draft1").submit(function() {
        $('.tagify--empty').css("border-color", "red")
        var hashtag1 = safeVal("#hashtag1");
        var hashtag2 = safeVal("#hashtag2");
        var hashtag3 = safeVal("#hashtag3");

        if (hashtag1 == "") {
            $("#hashtag1").closest(".input-group").addClass("eror")
        } else {
            $("#hashtag1").closest(".input-group").removeClass("eror")
        }
        if (hashtag2 == "") {
            $("#hashtag2").closest(".input-group").addClass("eror")
        } else {
            $("#hashtag2").closest(".input-group").removeClass("eror")
        }
        if (hashtag3 == "") {
            $("#hashtag3").closest(".input-group").addClass("eror")
        } else {
            $("#hashtag3").closest(".input-group").removeClass("eror")
        }
    })

    function confirmSubmit() {
        var count;
        var countPrice;
        $.ajax({
                url: "{{ url('get-social-connect') }}",
            })
            .done(function(data) {
                if (data.social_connect == null) {
                    $('#errorText').html(
                        '<div class="worning_point">Please connect at least one social media to proceed</div>'
                        );
                    $("#collection1 .next").addClass("disabled")
                    count = 1;
                } else {
                    count = 0;
                }
            });

        if (count != 1) {
            countPrice = 0;
            var values = $("input[name='price[]']").map(function() {
                return $(this).val();
            }).get();
            $.each(values, function(index, value) {
                if (value > 0) {
                    countPrice = 1;
                }
            });
            if (countPrice == 0) {
                count = 1;

                // $('#confirmreset').modal('show');
                $('#errorText').html('Please select atleast one of Advertising Methods and its price should be more than 0$!');
            } else {
                count = 0;
            }
        }

        if (count == 0) {
            return true;
        } else {
            return false;
        }
    }

    function confirmReset() {
        var agree = confirm("If you save in draft-mode, you will be not listed in marketplace.");
        if (agree)
            return true;
        else
            return false;
    }

    $('#selectCountry').change(function() {
        $("#selectCity").empty();
        var countryVal = safeVal(this);
        if (countryVal) {
            $.get("{{ url('fetch-states') }}", {
                country: countryVal
            }, function(res) {
                $("#selectCity").append(res);
            });
        }
    });

    $('#selectState').change(function() {
        $("#selectCity").empty();
        var stateVal = safeVal(this);
        if (stateVal) {
            $.get("{{ url('fetch-cities') }}", {
                state: stateVal
            }, function(res) {
                $("#selectCity").append(res);
                $("#selectCity").selectpicker('refresh');
            });
        }
    });


    function validate(e) {
        var text = e.target.value;

        text = text.substring(1);
        $.ajax({
            type: "GET",
            url: "{{ URL::to('/hashtags') }}/" + text,
            dataType: "json",
        }).done(function(response) {
            if (response && Array.isArray(response)) {
                var len = response.length;
                $("#searchResult").empty();
                for (var i = 0; i < len; i++) {
                    var tags = response[i];
                    $("#searchResult").append("<li >" + tags + "</li>");
                }
            }
        });
    }

    $("input[name='influencer_type']").change(function() {
        if (safeVal("input[type='radio'].influencer_type:checked") == 'Real personality') {
            $(".gender").attr('required', 'true');
            $("#viewgender").show();
        } else {
            $(".gender").removeAttr('required');
            $("#viewgender").hide();
        }
    });

    $(document).ready(function(event) {
        $(document).on('submit', '#configuration', function(e) {
            var formData = new FormData($(this)[0]);
            $.ajax({
                type: "POST",
                url: "{{ URL::to('/influencer') }}",
                data: formData,
                processData: false,
                contentType: false,
                dataType: "json",
                success: function(data) {
                    $(".wizardPopupOuter").hide();
                    toastr.success(data.msg);

                    $("#javascript_alert_msg").css("display", "block");
                    $("#javascript_alert_msg").slideDown().html(
                        '<div class="alert alert-success custom-alert-success animated  flash  text-center" style="margin-top: 38px!important;">' +
                        data.msg + ' </div>');
                    $("body").removeClass("hidescroll")
                }
            });

        });


        if (safeVal("select[name=sharecontent_media]") == "" && safeVal("select[name=video_media]") == "" && safeVal("select[name=livestream_media]") == "") {
            $("div[data-title=collection3]").addClass("disabled")
            $("div[data-title=collection4]").addClass("disabled")
            $("div[data-title=collection5]").addClass("disabled")
        }

        if(safeVal("#stripe_status") == "connected") {
            $("#tablink0").removeClass("disabled");

            $("#collection0 [required]").each(function(){
                var val = safeVal(this);
                if (val != null && val != "" && val != 0 && val != " ") {
                    @if((isset($social_connect_media) && $social_connect_media->count()>0))
                        $("#tablink1").removeClass("disabled")
                    @endif
                }
            })
            $("#collection2 [required]").each(function(){
                var val = $("#collection2 [required]").val();
                if (val && val.length > 0) {
                    $("#tablink2").removeClass("disabled")
                }
            })
            $("#collection3 [required]").each(function(){
                var val = $("#collection3 [required]").val();
                if (val && val.length > 0) {
                    $("#tablink3").removeClass("disabled")
                }
            })

            var collection4Val = $("#collection4 [name=collection]").val();
            if (collection4Val && collection4Val.length > 0) {
                $("#tablink4").removeClass("disabled")
            }
            var collection5Val = $("#collection5 [name=collection]").val();
            if (collection5Val && collection5Val.length > 0) {
                $("#tablink5").removeClass("disabled")
            }
        }
    });

    function refreshParent() {
        $("#errorMji").hide();
        $.ajax({
            url: "{{ url('latest-social-connect') }}",
        })
        .done(function(data) {
            $(".connectWith").html(data);
        });
        $.ajax({
            url: "{{ url('latest-advertising') }}",
        })
        .done(function(data) {
            $(".connectPrising").html(data);
        });
    }


    function refreshParentError() {
        refreshParent();
        $("#errorMji").show();
        $(".closeErrorNow").click(function() {
            $("#errorMji").hide();
            {{ Session::forget('influencer_error') }}
            {{ Session::forget('influencer_follower_error') }}
        });
    }

    $(function() {
        $('#zip_code').on('keypress', function(e) {
            if (e.which == 32) {
                return false;
            }
        });
    });

    $(document).click(function(event) {
        var nextlink = $(".nextLinkReq");
        if (nextlink && nextlink.length > 0) {
            var hasTarget = nextlink.has(event.target);
            var formControl = nextlink.closest(".floating-label").find(".form-control");
            var formControlHas = formControl.has(event.target);
            var select2Container = nextlink.closest(".floating-label").find(".select2-container");
            var select2ContainerHas = select2Container.has(event.target);

            if (!nextlink.is(event.target) &&
                (!hasTarget || hasTarget.length === 0) &&
                !formControl.is(event.target) &&
                (!formControlHas || formControlHas.length === 0) &&
                !select2Container.is(event.target) &&
                (!select2ContainerHas || select2ContainerHas.length === 0)) {
                $(".nextLinkReq").hide();
                $(".nextLinkOnl").hide();
            }
        }
    });

    $(document).ready(function() {
        $.ajax({
            url: "{{ url('get-influencer-data') }}",
        })
        .done(function(data) {
            // Data received successfully
        });



        var collection = "{{ isset($collection) ? $collection : 0 }}";
        if (parseInt(collection) > 0) {
            $(".next").closest('.steps_con').hide().removeClass("formActive");
            $(".ser_op").removeClass("current");
            $(".ser_op[data-title=collection" + collection + "]").addClass("current");
            $(".steps_con[data-content=collection" + collection + "]").show().addClass("formActive");
        } else {
            $(".steps_con").first().show().addClass("formActive");
            $(".ser_op").first().addClass("current");
        }

        $(window).keydown(function(event) {
            if (event.keyCode == 13) {
                event.preventDefault();
                return false;
            }
        });
    });

    $('#draft1').submit(function(e) {
        if ($("#hashtag1").hasClass("parsley-error")) {
            $("#hashtag1").closest(".input-group").addClass("errorDiv")
        } else {
            $("#hashtag1").closest(".input-group").removeClass("errorDiv")
        }
    });

    $(document).ready(function() {
        $('.shareconent').each(function() {
            var mediaLink = $(this).val();
            $(this).closest(".select_media ").addClass(mediaLink);
        });
    });

    $(document).on("click", ".back", function() {
        var step_id = $(this).closest(".steps_con").attr("id").replace(/collection/, '');
        $("#tablink" + step_id).removeClass("current");
        $("#tablink" + (step_id - 1)).addClass("current");
        $("#collection" + step_id).hide();
        $("#collection" + (step_id - 1)).show();
    })

    $(document).on("click", ".campaign-type-content input", function() {
        var get_class = $(this).closest(".campaign-type").attr("class").replace(
            "social_media_radio campaign-type ", "")
        if ($(this).is(":checked")) {
            $(".d-flex." + get_class).find(".react-action-price-one input.inputval").prop("disabled", false)
            $(".d-flex." + get_class).find(".react-action-price-one").removeClass("disabled-input")
            $(".d-flex." + get_class).find("[type=checkbox]").prop("checked", true)

        } else {
            $(".d-flex." + get_class).find(".react-action-price-one input.inputval").prop("disabled", true)
            $(".d-flex." + get_class).find(".react-action-price-one").addClass("disabled-input")
            $(".d-flex." + get_class).find("[type=checkbox]").prop("checked", false)
        }
        var checkedInputs = $("#collection3 .selected-media-action input[type='checkbox']:checked");
        if (checkedInputs && checkedInputs.length == 0) {
            $("#tablink4").addClass("disabled")
        }
    })
    $('#rqrIn3country').select2().change(function(){
        if( safeVal(this) == '') {
            $("#rqrIn5").prop('disabled', true);;
        } else {
            $("#rqrIn5").removeAttr("disabled");
        }

        $("#rqrIn5").empty();

        $.get("{{url('fetch-states')}}", {country: safeVal(this)}, function(res){
            $("#rqrIn5").append(res);
            $("#rqrIn5").select2('refresh');
        });
    });
</script>
@endsection

<div class="page_tab new_steps">
    <div class="mobile-step-detail">
        <div class="steps-cont"></div>
        <div class="steps-which">Marketplace</div>
    </div>
    <div id="latest-influencer-lists">
        {{-- Data will be loaded from ajax --}}
    </div>
    <input type="hidden" id="delected-influncer" value="" data-parsley-errors-container="#error-select-influncer" data-parsley-error-message="" required>
    <div class="step-nevigationbutton">
        <div class="nav-left" data-from-step="">
            <img src="{{ asset('/assets/front-end/images/icons/step-left.svg') }}" alt="">
        </div>
        <div class="backtop" id="backtop">
            <img src="{{ asset('/assets/front-end/images/icons/warenkorb_button.png') }}" alt="">
        </div>
        <div class="influncer-cart">
            <span class="influncer-cart-count">0</span>
            <img src="{{ asset('/assets/front-end/images/icons/influncer-cart.svg') }}" alt="">
        </div>

        <div class="selected-influncer-box">
            <img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}" class="close_inf_box" alt="">
            <div class="selected-influncer-contr"></div>
            <div class="selected-influncer-data">
                <div class="data-detail">
                    <div class="uj">
                        <img src="{{ asset('/assets/front-end/images/icons/req-money.svg') }}" alt="">
                    </div>
                    Subtotal:<span id="subtotal" data-subtotal="0">0.00</span> €
                </div>
                <div class="data-detail">
                    <div class="uj">
                        <img src="{{ asset('/assets/front-end/images/icons/icon-eye.svg') }}" alt="">
                    </div>
                    <span id="follower" data-followers="0">0</span> follower
                </div>
            </div>
        </div>
        <span id="error-select-influncer d-none"></span>
        <div class="nav-right">
            <img src="{{ asset('/assets/front-end/images/icons/step-right.svg') }}" alt="">
        </div>
    </div>
    <div class="filter-overlay"></div>
</div>

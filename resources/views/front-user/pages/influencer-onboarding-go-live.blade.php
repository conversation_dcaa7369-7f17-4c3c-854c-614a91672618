<div class="mobile-step-detail">
    <div class="steps-cont">STEP 06</div>
    <div class="steps-which">Go Live</div>
</div>
@if ($user->activate == '2')
    <form action="{{ url('/save-influencer-form') }}" method="post" data-parsley-validate id="draft4">
        @csrf
        <input type="hidden" name="advertising_method_price" value="true">
        <div class="informationDiv advertisingPriceMethod1">
            @include('front-user.pages.influencer-pricing-choose')
        </div>
    </form>
@else
    <form action="{{ url('/save-influencer-form') }}" method="post" id="draft5">
        @csrf
        <div class="informationDiv">
            <ul class="nav nav-tabs" id="myTab" role="tablist"></ul>
            <div class="tab-content connectPublish" id="myTabContent">
                @include('front-user.pages.influencer-publish')
                <div class="offset-3 col-6 text-center lets_go_btn">
                    <input type="hidden" name="publish" value="Publish">
                    <input type="hidden" name="Publishonline" value="PublishOnline">
                    <button type="submit" class="button-ccg new-style newpost float-none publish" name="PublishOnline" value="PublishOnline">Let’s go online!</button>
                </div>
            </div>
            <div class="step-nevigationbutton">
                <div class="nav-left back me-2" id="Back_step4">
                    <img src="{{ asset('assets/front-end/images/icons/step-left.svg') }}" alt="">
                </div>
                <div class="nav-right next ms-auto">
                    <img src="{{ asset('assets/front-end/images/icons/step-right.svg') }}" alt="" style="background: #d5cbe9 !important; cursor: default;">
                </div>
            </div>
        </div>
    </form>
@endif
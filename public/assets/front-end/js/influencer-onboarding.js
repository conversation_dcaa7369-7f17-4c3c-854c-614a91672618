/**
 * Influencer Onboarding Step Management System
 * Handles multi-step form navigation, validation, and URL hash management
 */

class InfluencerOnboardingManager {
    constructor() {
        this.currentStep = 'profile-information';
        this.steps = [
            'profile-information',
            'social-media', 
            'general-information',
            'target-group',
            'campaign-types',
            'go-live'
        ];
        this.completedSteps = [];
        this.isSubmitting = false;
        
        this.init();
    }

    /**
     * Initialize the onboarding manager
     */
    init() {
        this.bindEvents();
        this.loadCurrentStepFromHash();
        this.updateStepDisplay();
        this.checkStripeConnection();
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Hash change event for browser navigation
        window.addEventListener('hashchange', () => {
            this.loadCurrentStepFromHash();
            this.updateStepDisplay();
        });

        // Navigation button events
        $(document).on('click', '.onboarding-next-btn', (e) => {
            e.preventDefault();
            this.handleNextStep();
        });

        $(document).on('click', '.onboarding-back-btn', (e) => {
            e.preventDefault();
            this.handlePreviousStep();
        });

        // Step tab click events
        $(document).on('click', '.step-tab', (e) => {
            e.preventDefault();
            const stepName = $(e.currentTarget).data('step');
            this.navigateToStep(stepName);
        });

        // Form submission events
        $(document).on('submit', '.onboarding-form', (e) => {
            e.preventDefault();
            this.handleFormSubmission(e.target);
        });

        // Country change event for city dropdown
        $(document).on('change', 'select[name="country"]', () => {
            this.loadCities();
        });
    }

    /**
     * Load current step from URL hash
     */
    loadCurrentStepFromHash() {
        const hash = window.location.hash.substring(1);
        if (hash && this.steps.includes(hash)) {
            this.currentStep = hash;
        } else {
            this.currentStep = 'profile-information';
            this.updateUrlHash();
        }
    }

    /**
     * Update URL hash
     */
    updateUrlHash() {
        window.location.hash = this.currentStep;
    }

    /**
     * Navigate to specific step
     */
    navigateToStep(stepName) {
        if (!this.steps.includes(stepName)) {
            console.error('Invalid step:', stepName);
            return;
        }

        // Check if user can access this step
        if (!this.canAccessStep(stepName)) {
            this.showError('Please complete previous steps first.');
            return;
        }

        this.currentStep = stepName;
        this.updateUrlHash();
        this.updateStepDisplay();
    }

    /**
     * Handle next step navigation
     */
    async handleNextStep() {
        if (this.isSubmitting) return;

        // Validate current step
        if (!this.validateCurrentStep()) {
            return;
        }

        // Save current step data
        const success = await this.saveCurrentStep();
        if (!success) {
            return;
        }

        // Navigate to next step
        const nextStepIndex = this.steps.indexOf(this.currentStep) + 1;
        if (nextStepIndex < this.steps.length) {
            this.navigateToStep(this.steps[nextStepIndex]);
        }
    }

    /**
     * Handle previous step navigation
     */
    handlePreviousStep() {
        const prevStepIndex = this.steps.indexOf(this.currentStep) - 1;
        if (prevStepIndex >= 0) {
            this.navigateToStep(this.steps[prevStepIndex]);
        }
    }

    /**
     * Update step display
     */
    updateStepDisplay() {
        // Hide all step contents
        $('.onboarding-step-content').addClass('d-none');
        
        // Show current step content
        $(`.onboarding-step-content[data-step="${this.currentStep}"]`).removeClass('d-none');
        
        // Update step indicators
        this.updateStepIndicators();
        
        // Update navigation buttons
        this.updateNavigationButtons();
        
        // Load step data if needed
        this.loadStepData();
    }

    /**
     * Update step indicators
     */
    updateStepIndicators() {
        $('.step-tab').each((index, element) => {
            const $tab = $(element);
            const stepName = $tab.data('step');
            
            // Remove all state classes
            $tab.removeClass('active completed disabled');
            
            if (stepName === this.currentStep) {
                $tab.addClass('active');
            } else if (this.completedSteps.includes(stepName)) {
                $tab.addClass('completed');
            } else if (!this.canAccessStep(stepName)) {
                $tab.addClass('disabled');
            }
        });
    }

    /**
     * Update navigation buttons
     */
    updateNavigationButtons() {
        const currentIndex = this.steps.indexOf(this.currentStep);
        
        // Back button
        const $backBtn = $('.onboarding-back-btn');
        if (currentIndex === 0) {
            $backBtn.hide();
        } else {
            $backBtn.show();
        }
        
        // Next button
        const $nextBtn = $('.onboarding-next-btn');
        if (currentIndex === this.steps.length - 1) {
            $nextBtn.text('Complete Onboarding');
        } else {
            $nextBtn.text('Next Step');
        }
    }

    /**
     * Check if user can access a step
     */
    canAccessStep(stepName) {
        const stepIndex = this.steps.indexOf(stepName);
        
        // Always allow access to first step
        if (stepIndex === 0) {
            return true;
        }
        
        // Check if all previous steps are completed
        for (let i = 0; i < stepIndex; i++) {
            if (!this.completedSteps.includes(this.steps[i])) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Validate current step
     */
    validateCurrentStep() {
        const $form = $(`.onboarding-step-content[data-step="${this.currentStep}"] form`);
        
        if ($form.length === 0) {
            return true;
        }

        // Use Parsley validation if available
        if (typeof $form.parsley === 'function') {
            return $form.parsley().validate();
        }

        // Basic HTML5 validation
        return $form[0].checkValidity();
    }

    /**
     * Save current step data
     */
    async saveCurrentStep() {
        const $form = $(`.onboarding-step-content[data-step="${this.currentStep}"] form`);
        
        if ($form.length === 0) {
            return true;
        }

        this.isSubmitting = true;
        this.showLoading();

        try {
            const formData = new FormData($form[0]);
            formData.append('step', this.currentStep);

            const response = await fetch('/save-onboarding-step', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.completedSteps = result.completed_steps || [];
                this.showSuccess(result.message || 'Step saved successfully');
                return true;
            } else {
                this.showError(result.message || 'Error saving step');
                this.displayValidationErrors(result.errors);
                return false;
            }
        } catch (error) {
            console.error('Error saving step:', error);
            this.showError('Network error occurred. Please try again.');
            return false;
        } finally {
            this.isSubmitting = false;
            this.hideLoading();
        }
    }

    /**
     * Load step data
     */
    async loadStepData() {
        try {
            const response = await fetch(`/get-onboarding-step-data?step=${this.currentStep}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.completedSteps = result.completed_steps || [];
                this.updateStepIndicators();
                // Additional step-specific data loading can be handled here
            }
        } catch (error) {
            console.error('Error loading step data:', error);
        }
    }

    /**
     * Handle form submission
     */
    async handleFormSubmission(form) {
        const $form = $(form);
        const stepName = $form.closest('.onboarding-step-content').data('step');
        
        if (stepName !== this.currentStep) {
            return;
        }

        await this.saveCurrentStep();
    }

    /**
     * Check Stripe connection status
     */
    checkStripeConnection() {
        const stripeStatus = $('#stripe_status').val();
        
        if (stripeStatus === 'not_connected') {
            $('.step-tab').not('[data-step="profile-information"]').addClass('disabled');
            this.showWarning('Please connect your Stripe account to continue with the onboarding process.');
        }
    }

    /**
     * Load cities based on selected country
     */
    async loadCities() {
        const countryId = $('select[name="country"]').val();
        
        if (!countryId) {
            $('select[name="city"]').html('<option value="">Select City</option>').prop('disabled', true);
            return;
        }

        try {
            const response = await fetch(`/fetch-states?country_id=${countryId}`);
            const cities = await response.json();
            
            let options = '<option value="">Select City</option>';
            cities.forEach(city => {
                options += `<option value="${city.id}">${city.name}</option>`;
            });
            
            $('select[name="city"]').html(options).prop('disabled', false);
        } catch (error) {
            console.error('Error loading cities:', error);
        }
    }

    /**
     * Display validation errors
     */
    displayValidationErrors(errors) {
        if (!errors) return;

        Object.keys(errors).forEach(field => {
            const $field = $(`[name="${field}"]`);
            const $errorContainer = $field.siblings('.error-message');
            
            if ($errorContainer.length) {
                $errorContainer.text(errors[field][0]).show();
            }
        });
    }

    /**
     * Show loading state
     */
    showLoading() {
        $('.onboarding-next-btn').prop('disabled', true).text('Saving...');
    }

    /**
     * Hide loading state
     */
    hideLoading() {
        $('.onboarding-next-btn').prop('disabled', false);
        this.updateNavigationButtons();
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        // Implementation depends on your notification system
        console.log('Success:', message);
    }

    /**
     * Show error message
     */
    showError(message) {
        // Implementation depends on your notification system
        console.error('Error:', message);
    }

    /**
     * Show warning message
     */
    showWarning(message) {
        // Implementation depends on your notification system
        console.warn('Warning:', message);
    }
}

// Initialize when document is ready
$(document).ready(() => {
    window.onboardingManager = new InfluencerOnboardingManager();
});

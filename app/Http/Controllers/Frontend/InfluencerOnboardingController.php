<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\AdminGamification;
use App\Models\AdminPricing;
use App\Models\AdvertisingMethodNewPrice;
use App\Models\Category;
use App\Models\Country;
use App\Models\Hashtag;
use App\Models\InfluencerDetail;
use App\Models\SmCampaign;
use App\Models\SocialConnect;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class InfluencerOnboardingController extends Controller
{
    // Define step configuration
    private const ONBOARDING_STEPS = [
        'profile-information' => [
            'id' => 1,
            'name' => 'Profile Information',
            'form_id' => 'profile-form',
            'endpoint' => '/my-profile',
            'validation_rules' => [
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'country' => 'required|exists:countries,id',
                'city' => 'required|exists:cities,id',
                'zip_code' => 'required|string|max:20',
                'vat_id' => 'required|string|min:9|max:11',
                'is_small_business_owner' => 'required|in:0,1'
            ]
        ],
        'social-media' => [
            'id' => 2,
            'name' => 'Social Media',
            'form_id' => 'social-media-form',
            'endpoint' => '/save-onboarding-step',
            'validation_rules' => []
        ],
        'general-information' => [
            'id' => 3,
            'name' => 'General Information',
            'form_id' => 'general-information-form',
            'endpoint' => '/save-onboarding-step',
            'validation_rules' => [
                'category_id' => 'required|array',
                'hashtags' => 'required|array|min:3|max:3'
            ]
        ],
        'target-group' => [
            'id' => 4,
            'name' => 'Select Target Group',
            'form_id' => 'target-group-form',
            'endpoint' => '/save-onboarding-step',
            'validation_rules' => [
                'influencer_type' => 'required|string',
                'gender' => 'required|string',
                'ages' => 'required|string',
                'content_language' => 'required|string',
                'content_attracts' => 'required|string'
            ]
        ],
        'campaign-types' => [
            'id' => 5,
            'name' => 'Campaign Types',
            'form_id' => 'campaign-types-form',
            'endpoint' => '/save-onboarding-step',
            'validation_rules' => []
        ],
        'go-live' => [
            'id' => 6,
            'name' => 'Go Live',
            'form_id' => 'go-live-form',
            'endpoint' => '/save-onboarding-step',
            'validation_rules' => []
        ]
    ];

    /**
     * Get step configuration by step name
     */
    private function getStepConfig(string $stepName): ?array
    {
        return self::ONBOARDING_STEPS[$stepName] ?? null;
    }

    /**
     * Get current step from URL hash or session
     */
    private function getCurrentStep(Request $request): string
    {
        $step = $request->get('step', 'profile-information');

        // Validate step exists
        if (!array_key_exists($step, self::ONBOARDING_STEPS)) {
            $step = 'profile-information';
        }

        return $step;
    }

    /**
     * Check if user can access a specific step
     */
    private function canAccessStep(string $stepName): bool
    {
        $stepConfig = $this->getStepConfig($stepName);
        if (!$stepConfig) {
            return false;
        }

        $currentStepId = $stepConfig['id'];

        // Always allow access to first step
        if ($currentStepId === 1) {
            return true;
        }

        // Check if previous steps are completed
        for ($i = 1; $i < $currentStepId; $i++) {
            if (!$this->isStepCompleted($i)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if a step is completed
     */
    private function isStepCompleted(int $stepId): bool
    {
        $user = Auth::user();

        switch ($stepId) {
            case 1: // Profile Information
                return !empty($user->first_name) &&
                       !empty($user->last_name) &&
                       !empty($user->country) &&
                       !empty($user->city) &&
                       !empty($user->zip_code) &&
                       !empty($user->vat_id) &&
                       $user->is_small_business_owner !== null;

            case 2: // Social Media
                return SocialConnect::where('user_id', Auth::id())->exists();

            case 3: // General Information
                $influencerDetail = InfluencerDetail::where('user_id', Auth::id())->first();
                $hashtags = Hashtag::where('user_id', Auth::id())->count();
                return $influencerDetail &&
                       !empty($influencerDetail->category_id) &&
                       $hashtags >= 3;

            case 4: // Target Group
                $influencerDetail = InfluencerDetail::where('user_id', Auth::id())->first();
                return $influencerDetail &&
                       !empty($influencerDetail->influencer_type) &&
                       !empty($influencerDetail->gender) &&
                       !empty($influencerDetail->ages) &&
                       !empty($influencerDetail->content_language) &&
                       !empty($influencerDetail->content_attracts);

            case 5: // Campaign Types
                return AdvertisingMethodNewPrice::where('user_id', Auth::id())->exists();

            case 6: // Go Live
                return $user->status == 1;

            default:
                return false;
        }
    }
    /**
     * Save onboarding step data
     */
    public function saveOnboardingStep(Request $request)
    {
        $stepName = $this->getCurrentStep($request);
        $stepConfig = $this->getStepConfig($stepName);

        if (!$stepConfig) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid step'
            ], 400);
        }

        // Check if user can access this step
        if (!$this->canAccessStep($stepName)) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot access this step. Please complete previous steps first.'
            ], 403);
        }

        // Validate form data
        $validator = Validator::make($request->all(), $stepConfig['validation_rules']);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Save step data based on step type
        try {
            $this->saveStepData($stepName, $request->all());

            return response()->json([
                'success' => true,
                'message' => 'Step saved successfully',
                'next_step' => $this->getNextStep($stepName),
                'completed_steps' => $this->getCompletedSteps()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error saving step data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Save step-specific data
     */
    private function saveStepData(string $stepName, array $data): void
    {
        $userId = Auth::id();

        switch ($stepName) {
            case 'general-information':
                $this->saveGeneralInformation($data, $userId);
                break;

            case 'target-group':
                $this->saveTargetGroup($data, $userId);
                break;

            case 'campaign-types':
                $this->saveCampaignTypes($data, $userId);
                break;

            case 'go-live':
                $this->saveGoLive($data, $userId);
                break;
        }
    }

    /**
     * Save general information step
     */
    private function saveGeneralInformation(array $data, int $userId): void
    {
        // Save hashtags
        if (isset($data['hashtags'])) {
            Hashtag::where('user_id', $userId)->delete();

            foreach ($data['hashtags'] as $hashtag) {
                if (!empty($hashtag)) {
                    Hashtag::create([
                        'tags' => $hashtag,
                        'user_id' => $userId
                    ]);
                }
            }
        }

        // Save or update influencer details
        $influencerData = [
            'category_id' => isset($data['category_id']) ? implode(',', $data['category_id']) : '',
        ];

        InfluencerDetail::updateOrCreate(
            ['user_id' => $userId],
            $influencerData
        );
    }

    /**
     * Save target group step
     */
    private function saveTargetGroup(array $data, int $userId): void
    {
        $influencerData = [
            'influencer_type' => $data['influencer_type'] ?? '',
            'gender' => $data['gender'] ?? '',
            'ages' => $data['ages'] ?? '',
            'content_language' => $data['content_language'] ?? '',
            'content_attracts' => $data['content_attracts'] ?? ''
        ];

        InfluencerDetail::updateOrCreate(
            ['user_id' => $userId],
            $influencerData
        );
    }

    /**
     * Save campaign types step
     */
    private function saveCampaignTypes(array $data, int $userId): void
    {
        if (isset($data['advertising_method'])) {
            // Delete existing advertising methods
            AdvertisingMethodNewPrice::where('user_id', $userId)->delete();

            $socialConnections = SocialConnect::where('user_id', $userId)->get();
            $pricingData = AdminGamification::where('select_type', 'Pricing & Rank')
                ->where('type', Auth::user()->trophy)
                ->first();

            foreach ($socialConnections as $mediaConnection) {
                $cptPrice = $mediaConnection->followers / 1000;

                // Save different campaign types based on form data
                $this->saveCampaignType($data, $userId, $mediaConnection, $pricingData, $cptPrice, 'boost', 'Boost me');
                $this->saveCampaignType($data, $userId, $mediaConnection, $pricingData, $cptPrice, 'reaction', 'Reaction video');
                $this->saveCampaignType($data, $userId, $mediaConnection, $pricingData, $cptPrice, 'survey', 'Survey');
            }
        }
    }

    /**
     * Save specific campaign type
     */
    private function saveCampaignType(array $data, int $userId, $mediaConnection, $pricingData, float $cptPrice, string $type, string $typeName): void
    {
        if (isset($data["type_{$type}"])) {
            $adminPricing = AdminPricing::where('media', ucfirst($mediaConnection->media))
                ->where('type', $type === 'reaction' ? 'Reaction-Video' : ucfirst($typeName))
                ->where(function ($query) {
                    $query->where('country', '=', Auth::user()->country)
                        ->orWhere('country', '=', 'Standard');
                })
                ->where(function ($query) use ($mediaConnection) {
                    $query->where('range', '>', $mediaConnection->followers)
                        ->orWhere('range', '=', 'All');
                })
                ->latest('id')
                ->first();

            if (isset($adminPricing->cpt)) {
                $calculatedCpt = $adminPricing->cpt * (intval($cptPrice) != 0 ? intval($cptPrice) : 1);
            } else {
                $calculatedCpt = 0;
            }

            $finalPrice = ($calculatedCpt * $pricingData->pricing) / 100;

            AdvertisingMethodNewPrice::create([
                'user_id' => $userId,
                'media' => $mediaConnection->media,
                'type' => $typeName,
                'type_price' => $finalPrice
            ]);
        }
    }

    /**
     * Save go live step
     */
    private function saveGoLive(array $data, int $userId): void
    {
        if (isset($data['Publishonline']) && $data['Publishonline'] == 'PublishOnline') {
            User::find($userId)->update(['activate' => '2']);
        }

        if (isset($data['publish'])) {
            $influencerDetail = InfluencerDetail::where('user_id', $userId)->first();
            if ($influencerDetail) {
                $influencerDetail->publish = $data['publish'];
                $influencerDetail->save();
            }

            if ($data['publish'] == 'Publish') {
                User::find($userId)->update(['status' => 1]);
            } elseif ($data['publish'] == 'Draft') {
                User::find($userId)->update(['status' => 0]);
            }
        }
    }

    /**
     * Get next step name
     */
    private function getNextStep(string $currentStep): ?string
    {
        $steps = array_keys(self::ONBOARDING_STEPS);
        $currentIndex = array_search($currentStep, $steps);

        if ($currentIndex !== false && $currentIndex < count($steps) - 1) {
            return $steps[$currentIndex + 1];
        }

        return null;
    }

    /**
     * Get previous step name
     */
    private function getPreviousStep(string $currentStep): ?string
    {
        $steps = array_keys(self::ONBOARDING_STEPS);
        $currentIndex = array_search($currentStep, $steps);

        if ($currentIndex !== false && $currentIndex > 0) {
            return $steps[$currentIndex - 1];
        }

        return null;
    }

    /**
     * Get completed steps
     */
    private function getCompletedSteps(): array
    {
        $completedSteps = [];

        foreach (self::ONBOARDING_STEPS as $stepName => $config) {
            if ($this->isStepCompleted($config['id'])) {
                $completedSteps[] = $stepName;
            }
        }

        return $completedSteps;
    }

    /**
     * Get step data for frontend
     */
    public function getStepData(Request $request)
    {
        $stepName = $this->getCurrentStep($request);

        if (!$this->canAccessStep($stepName)) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot access this step'
            ], 403);
        }

        $data = $this->prepareStepData($stepName);

        return response()->json([
            'success' => true,
            'step' => $stepName,
            'data' => $data,
            'completed_steps' => $this->getCompletedSteps(),
            'can_navigate' => $this->getNavigationState($stepName)
        ]);
    }

    /**
     * Prepare data for specific step
     */
    private function prepareStepData(string $stepName): array
    {
        $userId = Auth::id();
        $baseData = [
            'user' => Auth::user(),
            'countries' => Country::get(),
            'categories' => Category::get(),
        ];

        switch ($stepName) {
            case 'profile-information':
                return array_merge($baseData, [
                    'cities' => DB::table('states')
                        ->leftjoin('cities', 'states.id', '=', 'cities.state_id')
                        ->select('cities.*')
                        ->where('states.country_id', Auth::user()->country)
                        ->get()
                ]);

            case 'social-media':
                return array_merge($baseData, [
                    'social_connections' => SocialConnect::where('user_id', $userId)->get()
                ]);

            case 'general-information':
                return array_merge($baseData, [
                    'influencer_detail' => InfluencerDetail::where('user_id', $userId)->first(),
                    'hashtags' => Hashtag::where('user_id', $userId)->get(),
                    'popular_hashtags' => $this->getPopularHashtags()
                ]);

            case 'target-group':
                return array_merge($baseData, [
                    'influencer_detail' => InfluencerDetail::where('user_id', $userId)->first()
                ]);

            case 'campaign-types':
                return array_merge($baseData, [
                    'social_connections' => SocialConnect::where('user_id', $userId)->get(),
                    'advertising_methods' => AdvertisingMethodNewPrice::where('user_id', $userId)->get(),
                    'sm_campaigns' => SmCampaign::get()
                ]);

            case 'go-live':
                return array_merge($baseData, [
                    'influencer_detail' => InfluencerDetail::where('user_id', $userId)->first(),
                    'social_connections' => SocialConnect::where('user_id', $userId)->get()
                ]);

            default:
                return $baseData;
        }
    }

    /**
     * Get popular hashtags
     */
    private function getPopularHashtags(): array
    {
        $popularHashtags = [];
        $hashtagCounts = Hashtag::groupBy('tags')->get();

        foreach ($hashtagCounts as $hashtag) {
            $count = Hashtag::where('tags', $hashtag->tags)->count();
            if ($count > 2) {
                $popularHashtags[] = $hashtag->tags;
            }
        }

        return $popularHashtags;
    }

    /**
     * Get navigation state for step
     */
    private function getNavigationState(string $stepName): array
    {
        return [
            'can_go_back' => $this->getPreviousStep($stepName) !== null,
            'can_go_forward' => $this->getNextStep($stepName) !== null && $this->isStepCompleted($this->getStepConfig($stepName)['id']),
            'previous_step' => $this->getPreviousStep($stepName),
            'next_step' => $this->getNextStep($stepName)
        ];
    }

    /**
     * Legacy method for backward compatibility
     * @deprecated Use saveOnboardingStep instead
     */
    public function saveOnboardingStepsData(Request $request)
    {
        // For backward compatibility, redirect to new method
        return $this->saveOnboardingStep($request);
    }
}

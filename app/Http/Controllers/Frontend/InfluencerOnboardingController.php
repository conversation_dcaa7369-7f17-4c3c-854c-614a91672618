<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\AdminGamification;
use App\Models\AdminPricing;
use App\Models\AdvertisingMethodNewPrice;
use App\Models\Category;
use App\Models\Country;
use App\Models\Hashtag;
use App\Models\InfluencerDetail;
use App\Models\SmCampaign;
use App\Models\SocialConnect;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

class InfluencerOnboardingController extends Controller
{
    public function saveOnboardingStepsData(Request $request)
    {
        $formData = request()->except(['_token']);

        if (isset($formData['Publishonline']) && $formData['Publishonline'] == 'PublishOnline') {
            User::find(Auth::id())->update(['activate' => '2']);
        }

        if ((isset($formData['update']) || (isset($formData['publish']) && $formData['publish']) == 'Draft') && isset($formData['collection'])) {
            Session::put('collection', $formData['collection']);
        } else {
            Session::forget('collection');
            Session::put('collection', '0');
        }

        $influencer_details = InfluencerDetail::where('user_id', Auth::id())->first();

        if (isset($influencer_details)) {
            if (isset($formData['publish'])) {
                $influncerdetail = InfluencerDetail::where('user_id', Auth::id())->first();
                $influncerdetail->publish = $formData['publish'];
                $influncerdetail->save();

                if ($formData['publish'] == 'Publish') {
                    User::find(Auth::id())->update(['status' => 1]);
                }

                if ($formData['publish'] == 'Draft') {
                    User::find(Auth::id())->update(['status' => 0]);
                }
            }

            if (isset($formData['draft'])) {
                $influncerdetail = InfluencerDetail::where('user_id', Auth::id())->first();
                $influncerdetail->publish = $formData['draft'];
                $influncerdetail->save();

                User::find(Auth::id())->update(['status' => 0]);
            }

            if (isset($formData['hashtags'])) {
                Hashtag::where('user_id', Auth::id())->delete();
                // $hashtags = explode(',', $formData['hidden-tags']);
                foreach ($formData['hashtags'] as $hashtag) {
                    if ($hashtag != '') {
                        $data = json_decode($hashtag);

                        $hashtags = array(
                            'tags' => $data[0]->value,
                            'user_id' => Auth::id()
                        );

                        Hashtag::create($hashtags);
                    }
                }
            }

            $influencer_detail = array(
                'category_id' => isset($formData['category_id']) ? implode(',', $formData['category_id']) : $influencer_details->category_id,
                'influencer_type' => isset($formData['influencer_type']) ? $formData['influencer_type'] : $influencer_details->influencer_type,
                'gender' => isset($formData['gender']) ? $formData['gender'] : $influencer_details->gender,
                'ages' => isset($formData['ages']) ? $formData['ages'] : $influencer_details->ages,
                'content_language' => isset($formData['content_language']) ? $formData['content_language'] : $influencer_details->content_language,
                'content_attracts' => isset($formData['content_attracts']) ? $formData['content_attracts'] : $influencer_details->content_attracts
            );

            InfluencerDetail::where('user_id', Auth::id())->update($influencer_detail);
        } else {
            if (isset($formData['hashtags'])) {
                // $hashtags = explode('#', $formData['hidden-tags']);
                foreach ($formData['hashtags'] as $hashtag) {
                    if ($hashtag != '') {
                        $data = json_decode($hashtag);

                        $hashtags = array(
                            'tags' => $data[0]->value,
                            'user_id' => Auth::id()
                        );

                        Hashtag::create($hashtags);
                    }
                }
            }

            $influencer_detail = array(
                'user_id' => Auth::id(),
                'category_id' => isset($formData['category_id']) ? implode(',', $formData['category_id']) : '',
                'influencer_type' => isset($formData['influencer_type']) ? $formData['influencer_type'] : '',
                'gender' => isset($formData['gender']) ? $formData['gender'] : '',
                'ages' => isset($formData['ages']) ? $formData['ages'] : '',
                'content_language' => isset($formData['content_language']) ? $formData['content_language'] : '',
                'content_attracts' => isset($formData['content_attracts']) ? $formData['content_attracts'] : ''
            );

            InfluencerDetail::create($influencer_detail);
        }

        if (isset($formData['publish']) && $formData['publish'] == 'Draft') {
            User::find(Auth::id())->update(['status' => 0]);
        }

        if (isset($formData['draft'])) {
            $influncerdetail = InfluencerDetail::where('user_id', Auth::id())->first();
            $influncerdetail->publish = $formData['draft'];
            $influncerdetail->save();
            User::find(Auth::id())->update(['status' => 0]);
        }

        if (isset($formData['advertising_method'])) {
            if ($detail = AdvertisingMethodNewPrice::whereUserId(Auth::id())->first()) {
                AdvertisingMethodNewPrice::whereUserId(Auth::id())->delete();
            }

            $social_connect_all = SocialConnect::where('user_id', Auth::id())->get();
            $pricingData = AdminGamification::where('select_type', 'Pricing & Rank')->where('type', Auth::user()->trophy)->first();

            foreach ($social_connect_all as $media_row) {
                $cpt_price = $media_row->followers / 1000;
                $pricingData_next = AdminGamification::where('id', $pricingData->id + 1)->first();

                if (isset($formData['type_boost'])) {
                    $boostData = AdminPricing::where('media', ucfirst($media_row->media))
                        ->where('type', 'Boost me')
                        ->where(function ($query) {
                            $query->where('country', '=', Auth::user()->country)
                                ->orWhere('country', '=', 'Standard');
                        })
                        ->where(function ($query) use ($media_row) {
                            $query->where('range', '>', $media_row->followers)
                                ->orWhere('range', '=', 'All');
                        })
                        ->latest('id')->first();

                    if (isset($boostData->cpt)) {
                        $boost_cpt = $boostData->cpt * ((intval($cpt_price) != 0) ? intval($cpt_price) : 1);
                    } else {
                        $boost_cpt = 0;
                    }

                    $boost = ($boost_cpt * $pricingData->pricing) / 100;

                    $influencer_detail2 = array(
                        'user_id' => Auth::id(),
                        'media' => $media_row->media,
                        'type' => 'Boost me',
                        'type_price' => $boost
                    );

                    AdvertisingMethodNewPrice::create($influencer_detail2);
                }

                if (isset($formData['type_reaction'])) {
                    $reactionData = AdminPricing::where('media', ucfirst($media_row->media))
                        ->where('type', 'Reaction-Video')
                        ->where(function ($query) {
                            $query->where('country', '=', Auth::user()->country)
                                ->orWhere('country', '=', 'Standard');
                        })
                        ->where(function ($query) use ($media_row) {
                            $query->where('range', '>', $media_row->followers)
                                ->orWhere('range', '=', 'All');
                        })
                        ->latest('id')->first();

                    if (isset($reactionData->cpt)) {
                        $reaction_cpt = $reactionData->cpt * ((intval($cpt_price) != 0) ? intval($cpt_price) : 1);
                    } else {
                        $reaction_cpt = 0;
                    }

                    $reaction = ($reaction_cpt * $pricingData->pricing) / 100;

                    $influencer_detail1 = array(
                        'user_id' => Auth::id(),
                        'media' => $media_row->media,
                        'type' => 'Reaction video',
                        'type_price' => $reaction
                    );

                    AdvertisingMethodNewPrice::create($influencer_detail1);
                }

                if (isset($formData['type_survey'])) {
                    $surveyData = AdminPricing::where('media', ucfirst($media_row->media))
                        ->where('type', 'Survey')
                        ->where(function ($query) {
                            $query->where('country', '=', Auth::user()->country)
                                ->orWhere('country', '=', 'Standard');
                        })
                        ->where(function ($query) use ($media_row) {
                            $query->where('range', '>', $media_row->followers)
                                ->orWhere('range', '=', 'All');
                        })
                        ->latest('id')->first();

                    if (isset($surveyData->cpt)) {
                        $survey_cpt = $surveyData->cpt * ((intval($cpt_price) != 0) ? intval($cpt_price) : 1);
                    } else {
                        $survey_cpt = 0;
                    }

                    $survey = ($survey_cpt * $pricingData->pricing) / 100;

                    $influencer_detail1 = array(
                        'user_id' => Auth::id(),
                        'media' => $media_row->media,
                        'type' => 'Survey',
                        'type_price' => $survey
                    );

                    AdvertisingMethodNewPrice::create($influencer_detail1);
                }
            }
        }


        if ($request->ajax()) {
            $user = User::find(Auth::id());
            $social_connect_twitter = SocialConnect::where('media', 'twitter')->where('user_id', Auth::id())->first();
            $social_connect_youtube = SocialConnect::where('media', 'youtube')->where('user_id', Auth::id())->first();
            $social_connect_twitch = SocialConnect::where('media', 'twitch')->where('user_id', Auth::id())->first();
            $social_connect_facebook = SocialConnect::where('media', 'facebook')->where('user_id', Auth::id())->first();
            $social_connect_instagram = SocialConnect::where('media', 'instagram')->where('user_id', Auth::id())->first();
            $social_connect_tiktok = SocialConnect::where('media', 'tiktok')->where('user_id', Auth::id())->first();
            $social_connect_snapchat = SocialConnect::where('media', 'snapchat')->where('user_id', Auth::id())->first();

            $socialFollowerCount = SocialConnect::whereUserId(Auth::id())->get()->sortByDesc('followers');
            $influencers = User::whereUserType('influencer')->where('id', '!=', Auth::id())->get();

            $category = Category::get();

            $influencer_detail = InfluencerDetail::where('user_id', Auth::id())->first();
            // dd($influencer_detail);
            $hashtags = Hashtag::where('user_id', Auth::id())->get('tags');

            $social_connect_media = SocialConnect::where('user_id', Auth::id())->first();

            $advertising_methods = AdvertisingMethodNewPrice::where('user_id', Auth::id())->groupBy('media')->get();

            $type_count = $advertising_methods->count();

            if (isset($advertising_methods[0]) && $advertising_methods[0] != '' && $advertising_methods[0]->media != '') {
                $advertising_methods[0]->media;
                $social_connect_media = SocialConnect::where('user_id', Auth::id())->where('media', $advertising_methods[0]->media)->first();
                if ($social_connect_media == '') {
                    $social_connect_media = SocialConnect::where('user_id', Auth::id())->first();
                }
            } else {
                $social_connect_media = SocialConnect::where('user_id', Auth::id())->first();
            }

            if (isset($social_connect_media)) {
                $advertising_admin_reaction = AdminPricing::where('media', ucfirst($social_connect_media->media))
                    ->where('type', 'Reaction-Video')
                    ->where(function ($query) {
                        $query->where('country', '=', Auth::user()->country)
                            ->orWhere('country', '=', 'Standard');
                    })
                    ->where(function ($query) use ($social_connect_media) {
                        $query->where('range', '>', $social_connect_media->followers)
                            ->orWhere('range', '=', 'All');
                    })
                    ->latest('id')->first();

                $advertising_admin_boost = AdminPricing::where('media', ucfirst($social_connect_media->media))
                    ->where('type', 'Boost me')
                    ->where(function ($query) {
                        $query->where('country', '=', Auth::user()->country)
                            ->orWhere('country', '=', 'Standard');
                    })
                    ->where(function ($query) use ($social_connect_media) {
                        $query->where('range', '>', $social_connect_media->followers)
                            ->orWhere('range', '=', 'All');
                    })
                    ->latest('id')->first();

                $advertising_admin_survey = AdminPricing::where('media', ucfirst($social_connect_media->media))
                    ->where('type', 'Survey')
                    ->where(function ($query) {
                        $query->where('country', '=', Auth::user()->country)
                            ->orWhere('country', '=', 'Standard');
                    })
                    ->where(function ($query) use ($social_connect_media) {
                        $query->where('range', '>', $social_connect_media->followers)
                            ->orWhere('range', '=', 'All');
                    })
                    ->latest('id')->first();
            } else {
                $advertising_admin_reaction = null;
                $advertising_admin_boost = null;
                $advertising_admin_survey = null;
            }

            $hashtag = $hashtags;

            $country = Country::get();
            // $social_connect = SocialConnect::where('user_id',Auth::id())->first();

            $social_connect_high = SocialConnect::where('user_id', Auth::id())->orderByRaw('CONVERT(followers, SIGNED) desc')->first();

            $social_connect = SocialConnect::where('user_id', Auth::id())->orderByRaw('CONVERT(followers, SIGNED) desc')->first();

            if ($social_connect == '') {
                $social_connect['medeia'] = 'facebook';
            }
            // $social_connect_data = SocialConnect::where('user_id',Auth::id())->get();
            $social_connect_data = SocialConnect::where('user_id', Auth::id())->get();



            $media = '';

            if ($media == '') {
                $sharecontentData = '';
            } else {
                $sharecontentData = InfluencerDetail::join('advertising_method_prices', 'advertising_method_prices.user_id', '=', 'influencer_details.user_id')
                    ->join('social_connects', function ($join) {
                        $join->on('social_connects.user_id', '=', 'influencer_details.user_id');
                    })
                    ->leftjoin('hashtags', 'hashtags.user_id', '=', 'influencer_details.user_id')
                    ->leftjoin('users', 'users.id', '=', 'influencer_details.user_id')
                    ->select('influencer_details.*', 'social_connects.media', 'social_connects.url', 'social_connects.picture', 'social_connects.name as username', 'social_connects.followers', 'advertising_method_prices.*', 'hashtags.tags', DB::raw("ABS(social_connects.followers - $media->followers) AS distance"))
                    ->where('influencer_details.publish', 'Publish')
                    // ->where('advertising_method_prices.sharecontent_media',Auth::user()->advertisingMethodPrice->sharecontent_media)
                    ->where('social_connects.media', Auth::user()->advertisingMethodPrice->sharecontent_media)
                    ->where('influencer_details.ages', Auth::user()->influencerDetail->ages)
                    ->where('users.id', '!=', Auth::id())->orderBy('distance')
                    ->first();
            }

            $hashtag_arr = [];
            $count = Hashtag::groupBy('tags')->get();
            foreach ($count as $row) {
                $count_arr = Hashtag::where('tags', $row['tags'])->count();
                if ($count_arr > 2) {
                    array_push($hashtag_arr, $row['tags']);
                }
            }

            $totalMedias = ['twitter', 'youtube', 'twitch', 'facebook', 'instagram', 'tiktok', 'snapchat'];
            $medias = [];

            foreach ($socialFollowerCount as $social) {
                $medias[] = $social->media;
            }

            $remainingMedias = array_diff($totalMedias, $medias);

            $collection = Session::get('collection');

            $social_connect_arr = [
                $social_connect_twitter,
                $social_connect_youtube,
                $social_connect_twitch,
                $social_connect_facebook,
                $social_connect_instagram,
                $social_connect_tiktok,
                $social_connect_snapchat,
            ];

            $newSocialConnectArr = [];
            foreach ($social_connect_arr as $sortArr) {
                if ($sortArr && $sortArr['media'] != 'twitter') {
                    $newSocialConnectArr[$sortArr['media']] = $sortArr['followers'];
                }
            }
            arsort($newSocialConnectArr);

            $sm_campaigns = SmCampaign::get();

            // dd($social_connect_high);

            return response([
                'status' => true,
                'msg' => 'Details saved successfully.',
                'collection' => $collection,
                'generalPage' => \View::make('front-user.pages.general-information', compact(
                    'user',
                    'social_connect_twitter',
                    'social_connect_youtube',
                    'social_connect_twitch',
                    'social_connect_facebook',
                    'social_connect_instagram',
                    'category',
                    'influencer_detail',
                    'hashtag',
                    'advertising_methods',
                    'country',
                    'social_connect',
                    'hashtag_arr',
                    'social_connect_tiktok',
                    'social_connect_snapchat',
                    'newSocialConnectArr',
                    'socialFollowerCount',
                    'remainingMedias',
                    'social_connect_media',
                    'advertising_admin_reaction',
                    'advertising_admin_boost',
                    'advertising_admin_survey',
                    'type_count',
                    'sm_campaigns',
                    'social_connect_high'
                ))->render(),

                'targetPage' => \View::make('front-user.pages.target-group', compact(
                    'user',
                    'social_connect_twitter',
                    'social_connect_youtube',
                    'social_connect_twitch',
                    'social_connect_facebook',
                    'social_connect_instagram',
                    'category',
                    'influencer_detail',
                    'hashtag',
                    'advertising_methods',
                    'country',
                    'social_connect',
                    'hashtag_arr',
                    'social_connect_tiktok',
                    'social_connect_snapchat',
                    'newSocialConnectArr',
                    'socialFollowerCount',
                    'remainingMedias',
                    'social_connect_media',
                    'advertising_admin_reaction',
                    'advertising_admin_boost',
                    'advertising_admin_survey',
                    'type_count',
                    'sm_campaigns',
                    'social_connect_high'
                ))->render(),

                'advertisingPage' => \View::make('front-user.pages.influencer-advertising-choose', compact(
                    'user',
                    'social_connect_twitter',
                    'social_connect_youtube',
                    'social_connect_twitch',
                    'social_connect_facebook',
                    'social_connect_instagram',
                    'category',
                    'influencer_detail',
                    'hashtag',
                    'advertising_methods',
                    'country',
                    'social_connect',
                    'hashtag_arr',
                    'social_connect_tiktok',
                    'social_connect_snapchat',
                    'newSocialConnectArr',
                    'socialFollowerCount',
                    'remainingMedias',
                    'social_connect_media',
                    'advertising_admin_reaction',
                    'advertising_admin_boost',
                    'advertising_admin_survey',
                    'type_count',
                    'sm_campaigns',
                    'social_connect_high'
                ))->render(),

                'advertisingPricePage' => \View::make('front-user.pages.influencer-pricing-choose', compact(
                    'user',
                    'social_connect_twitter',
                    'social_connect_youtube',
                    'social_connect_twitch',
                    'social_connect_facebook',
                    'social_connect_instagram',
                    'category',
                    'influencer_detail',
                    'hashtag',
                    'advertising_methods',
                    'country',
                    'social_connect',
                    'hashtag_arr',
                    'social_connect_tiktok',
                    'social_connect_snapchat',
                    'newSocialConnectArr',
                    'socialFollowerCount',
                    'remainingMedias',
                    'social_connect_media',
                    'advertising_admin_reaction',
                    'advertising_admin_boost',
                    'advertising_admin_survey',
                    'type_count',
                    'sm_campaigns',
                    'social_connect_high'
                ))->render(),

                'publishPage' => \View::make('front-user.pages.influencer-publish', compact(
                    'user',
                    'social_connect_twitter',
                    'social_connect_youtube',
                    'social_connect_twitch',
                    'social_connect_facebook',
                    'social_connect_instagram',
                    'category',
                    'influencer_detail',
                    'hashtag',
                    'advertising_methods',
                    'country',
                    'social_connect',
                    'hashtag_arr',
                    'social_connect_tiktok',
                    'social_connect_snapchat',
                    'newSocialConnectArr',
                    'socialFollowerCount',
                    'remainingMedias',
                    'social_connect_media',
                    'advertising_admin_reaction',
                    'advertising_admin_boost',
                    'advertising_admin_survey',
                    'type_count',
                    'sm_campaigns',
                    'social_connect_high'
                ))->render(),
            ]);
        }

        return redirect('/influencer-onboarding')->with('success', 'Details saved successfully.');
    }
}
